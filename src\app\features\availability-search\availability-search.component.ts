import {Component, OnInit, ViewChild} from '@angular/core';
import {FormArray, FormBuilder, FormControl, FormGroup} from '@angular/forms';
import {AvailabilitySearchService} from '../../core/services/availability-search.service';
import {state, style, trigger} from '@angular/animations';
import {SnackBarComponent} from '../../shared/components/snack-bar/snack-bar.component';
import {MatSnackBar} from '@angular/material/snack-bar';
import {SearchBoxComponent} from '../../shared/components/search-box/search-box.component';
import {MAT_DATE_LOCALE} from '@angular/material/core';
import {DatePipe} from '@angular/common';
import {Router} from '@angular/router';
import {MatDialog} from '@angular/material/dialog';
import { Restangular } from 'ngx-restangular';
import {SearchConfigDialogComponent} from './availability-search-response/search-config-dialog/search-config-dialog.component';
import { HttpStatusCode } from '@angular/common/http';

const PERFIL_EMPRESA_INTEGRACAO: string = 'INTEGRACAO';

const TRECHOS = {
  codAeroportoOrigem: [null],
  codAeroportoDestino: [null],
  dataEmbarque: [null]
};

@Component({
  selector: 'app-availability-search',
  templateUrl: './availability-search.component.html',
  styleUrls: ['./availability-search.component.css'],
  animations: [
    trigger('loadingState', [
      state('loading', style({
        animation: 'spin 1.5s linear infinite'
      })),
      state('loaded', style({
        animation: 'none',
      }))
    ])
  ],
  providers: [
    {provide: MAT_DATE_LOCALE, useValue: 'pt-BR'},
    DatePipe
  ]
})
export class AvailabilitySearchComponent implements OnInit {
  private readonly EMPTY: number = 0;

  @ViewChild('filialField')
  filialControl: SearchBoxComponent;
  step = 0;
  response = null;


  shouldResetSearchBoxForm = false;
  hasLastSearchResponse = true;
  loadingSearch = false;
  availabilityFormGroup: FormGroup;
  filterId = new FormControl(null);
  iconFilterById = 'downloading';
  availResponse: any;
  invalidAgenciaLabel: string;

  public readonly trechos = TRECHOS;

  get trechosFormArray() {
    return (this.availabilityFormGroup.controls.trechos as FormArray);
  }

  data = {
    cache: false,
    vooDireto: false,
    vooExcluido: false,
    xmls: false,
    numAdt: null,
    numChd: null,
    numInf: null,
    cabine: undefined,
    usuario: null,
    filial: null,
    agencia: null,
    grupo: null,
    produtos: null,
    dataReserva: null,
    tipoBusca: null,
    sistEmis: null,
    flightNumber: null,
    fareFamily: null,
    fareType: null,
    baggage: null,
    fareClass: null,
    fareBasis: null,
    designatorCode: null,
    maxNumberOfResults: null,
    ciasAereas: [],
    trechos: [],
  };

  constructor(private _formBuilder: FormBuilder,
              private availabilityService: AvailabilitySearchService,
              private snackBar: MatSnackBar,
              private datePipe: DatePipe,
              private restangular: Restangular,
              private router: Router,
              private dialog: MatDialog, ) {
  }

  async ngOnInit(): Promise<void> {
    this.availabilityFormGroup = this._formBuilder.group({
      numAdt: [this.data ? this.data.numAdt : null],
      numChd: [this.data ? this.data.numChd : null],
      numInf: [this.data ? this.data.numInf : null],
      cabine: [this.data ? this.data.cabine : undefined],
      usuario: [this.data ? this.data.usuario : null],
      filial: [this.data ? this.data.filial : null],
      agencia: [this.data ? this.data.agencia : null],
      grupo: [this.data ? this.data.grupo : null],
      produtos: [this.data ? this.data.produtos : null] ,
      dataReserva: [this.data ? this.data.dataReserva : null],
      tipoBusca: [this.data ? this.data.tipoBusca : null],
      sistEmis: [this.data ? this.data.sistEmis : null],
      flightNumber: [this.data ? this.data.flightNumber : null],
      fareFamily: [this.data ? this.data.fareFamily : null],
      fareType: [this.data ? this.data.fareType : null],
      baggage: [this.data ? this.data.baggage : null],
      fareClass: [this.data ? this.data.fareClass : null],
      fareBasis: [this.data ? this.data.fareBasis : null],
      designatorCode: [this.data ? this.data.designatorCode : null],
      maxNumberOfResults: [this.data ? this.data.maxNumberOfResults : null],
      ciasAereas: [this.data ? this.data.ciasAereas : []],

      cache: [this.data ? this.data.cache : false],
      vooDireto: [this.data ? this.data.vooDireto : false],
      vooExcluido: [this.data ? this.data.vooExcluido : false],
      xmls: [this.data ? this.data.ciasAereas : false],

      trechos: this._formBuilder.array([this._formBuilder.group(TRECHOS)]),
    });

    while (this.trechosFormArray.length > 0) {
      this.trechosFormArray.removeAt(0);
    }

    while (this.trechosFormArray.length < 1) {
      this.trechosFormArray.push(this._formBuilder.group(this.trechos));
    }

    if (this.availabilityService.getLastSearch) {
      await this.setLoadItem(this.availabilityService.getLastSearch);
      if (this.availabilityService.getLastSearchedIds !== 'null') {
        this.filterId.setValue(this.availabilityService.getLastSearchedIds);
      }
        this.fligthsOrganize()
    }
  }


  getFilterChips() {
    const filters = this.availabilityFormGroup.value || {};
    const requiredKeys = ['baggage', 'designatorCode', 'fareBasis', 'fareClass', 'fareFamily', 'fareType', 'maxNumberOfResults', 'flightNumber'];

    const labels = {
      baggage: 'Bagagem',
      designatorCode: 'Designador Base',
      fareBasis: 'Base de Tarifa',
      fareClass: 'Classe de Tarifaria',
      fareFamily: 'Família Tarifária',
      fareType: 'Tipo de Tarifa',
      maxNumberOfResults: 'Numero Máximo de pesquisas',
      flightNumber: 'Número do Voo'
    };
  
    const baggageTranslations = {
      COMSEM: 'Recomendações Com e Sem bagagem',
      COM: 'Recomendações apenas com bagagem',
      SEM: 'Recomendações apenas Sem bagagem',
    };
  
    const fareTypeTranslations = {
      PU: 'Pública',
      PR: 'Privada',
      AMB: 'Ambas', 
    };
  
    return requiredKeys
      .filter(key => filters[key] !== undefined && filters[key] !== null)
      .map(key => {
        let value = filters[key];
        if (key === 'baggage' && baggageTranslations[value]) {
          value = baggageTranslations[value];
        }
        if (key === 'fareType' && fareTypeTranslations[value]) {
          value = fareTypeTranslations[value];
        }
        return {
          key: labels[key] || key, 
          value: value
        };
      });
  }


   fligthsOrganize(){
    const trechosArray = this.availabilityFormGroup.get('trechos') as FormArray;
    trechosArray.controls.forEach(async (control) => {
    const trecho = control.value;
    let nomeOrigem = await this.callAeroportoNome(trecho.codAeroportoOrigem.codigo);
    let nomeDestino = await this.callAeroportoNome(trecho.codAeroportoDestino.codigo);
    trecho.codAeroportoOrigem.codigo = trecho.codAeroportoOrigem.codigo as string + nomeOrigem ;
    trecho.codAeroportoDestino.codigo = trecho.codAeroportoDestino.codigo as string + nomeDestino;
    control.setValue(trecho);
      });
      this.availabilityFormGroup.setControl('trechos', trechosArray);
  }

  private async callAeroportoNome(value: any): Promise<any> {

    return new Promise((resolve, reject) => {
      this.restangular.all('aeroportos').get('', {
        codigo: value
      }).subscribe(
        (responses) => {
          let nome = ' - ' + responses.body[0].nome as string;
          resolve(nome);
        },
        (error) => {
          reject(error);
        }
      );
    });
  }

  async loadFilterById() {
    if (this.filterId.valid) {
      this.resetForm();
      this.shouldResetSearchBoxForm = true;
      this.iconFilterById = 'sync';
      this.availabilityFormGroup.disable();
      await this.availabilityService.loadFilterById(this.filterId.value).then(async data =>  {
        await this.setLoadItem(data);
        this.iconFilterById = 'downloading';
        this.availabilityFormGroup.enable();
        this.availabilityService.storeLastLocalSearch(this.filterId.value, data);
        this.fligthsOrganize();
              this.shouldResetSearchBoxForm = false;
              location.reload();
      }).catch(reason => {
        this.snackBar.openFromComponent(SnackBarComponent, {
          data: {
            title: 'Alerta',
            subTitle: reason.data.mensagem,
            buttons: [{label: 'Fechar', action: 'close', color: 'warn'}]
          }
        });
        this.iconFilterById = 'downloading';
      });
    } else {
      this.filterId.markAllAsTouched();
      this.snackBar.openFromComponent(SnackBarComponent, {
        data: {
          title: 'Alerta',
          subTitle: 'Verifique o campo esta com valores corretos',
          buttons: [{label: 'Fechar', action: 'close', color: 'warn'}]
        }
      });
    }
  }

  private async setLoadItem(data: any) {
    if (data.data) {
      data = data.data;
    } else if (data.body) {
      data = data.body;
    }

    data.usuario.empresa = await this.availabilityService.refreshEmpresa(data.usuario);

    const {
      cache,
      vooDireto,
      vooExcluido,
      xmls,
      numAdt,
      numChd,
      numInf,
      cabine,
      usuario,
      filial,
      agencia,
      grupo,
      produtos,
      dataReserva,
      tipoBusca,
      sistEmis,
      flightNumber,
      fareFamily,
      fareType,
      baggage,
      fareClass,
      fareBasis,
      designatorCode,
      maxNumberOfResults,
      trechos,
      ciasAereas,
    } = data;
    this.data = data;
    
    const trechosMapped = trechos.sort((a, b) => {
      const dateA = a.dataEmbarque[0] * 10000 + a.dataEmbarque[1] * 100 + a.dataEmbarque[2];
      const dateB = b.dataEmbarque[0] * 10000 + b.dataEmbarque[1] * 100 + b.dataEmbarque[2];

      if (dateA < dateB) {
        return -1;
      }
      if (dateA > dateB) {
        return 1;
      }
      return 0;
    }).map(trecho => {
      const month = (trecho.dataEmbarque[1]) < 10 ? '0' + (trecho.dataEmbarque[1]) : trecho.dataEmbarque[1];
      const day = (trecho.dataEmbarque[2]) < 10 ? '0' + (trecho.dataEmbarque[2]) : trecho.dataEmbarque[2];
      const date = trecho.dataEmbarque[0] + '-' + month + '-' + day;

      return {
        codAeroportoOrigem: {codigo: trecho.codAeroportoOrigem},
        codAeroportoDestino: {codigo: trecho.codAeroportoDestino},
        dataEmbarque: date
      };
    });

    while (this.trechosFormArray.length > 0) {
      this.trechosFormArray.removeAt(0);
    }
    while (this.trechosFormArray.length < trechosMapped.length) {
      this.trechosFormArray.push(this._formBuilder.group(this.trechos));
    }

    this.availabilityFormGroup.patchValue({
      cache,
      vooDireto,
      vooExcluido,
      xmls,
      numAdt,
      numChd,
      numInf,
      cabine: this.toCamelCase(cabine),
      usuario: this.usuarioTransform(usuario),
      filial: this.filialTransform(filial),
      agencia,
      grupo: this.filialTransform(grupo),
      produtos,
      dataReserva,
      tipoBusca,
      sistEmis,
      flightNumber,
      fareFamily,
      fareType,
      baggage,
      fareClass,
      fareBasis,
      designatorCode,
      maxNumberOfResults,
      trechos: trechosMapped,
      ciasAereas
      }
    );
  }

  public toCamelCase(str) {
    const camel = str.replace(/(?:^\w|[A-Z]|\b\w)/g, function(word, index) {
      return index === 0 ? word.toUpperCase() : word.toLowerCase();
    }).replace(/\s+/g, '');
    return camel;
  }

  public usuarioTransform(item: any) {
    return item === null || item === undefined || item.id === undefined ? null : {
      ...item,
      nomeTratado: `${item.id} - ${item.nome} ${item.sobrenome}`
    };
  }

  public filialTransform(item: any) {
    return item === null || item === undefined || item.nome === undefined ? null : {
      ...item,
      nomeTratado: `${item.referencia} - ${item.nome}`
    };
  }

  public agenciaTransform(item: any) {
    return item === null || item === undefined || item.id === undefined ? null : {
      ...item,
      nomeTratado: `${item.referencia} - ${item.nomeFantasia}`
    };
  }

  public vooTransform(item: any){
      item = { ... item, codigo: `${item.codigo} - ${item.nome}`}
      const parts = item.codigo.split(" - ");
      if (parts[1] === parts[2]) {
        parts.pop();
      }
      item.codigo = parts.join(" - ");
      return item;
  }

  public usuarioChange(value: any): void {
    let filterValue: any;
    if (value !== null) {
      filterValue = {empresa: value.empresa?.nome};
    }
    if (value.length === 0) {
      this.availabilityFormGroup.get('usuario').setValue('');
      filterValue = {empresa: null};
    }
    this.filialControl.filters = filterValue;
  }

  searchFlights(xmls) {
    if (this.availabilityFormGroup.valid && this.loadingSearch === false) {
        this.loadingSearch = true;
        let values = this.availabilityFormGroup.value;
        values.cabine = values.cabine || 'UNDEF';

        values = Object.keys(values).reduce((acc, key) => {
          if (values[key] !== null) {
            acc[key] = values[key];
          }
          return acc;
        }, {});

        if (values.trechos[0].codAeroportoOrigem.codigo != null) {
            values.trechos = values.trechos.map((trecho) => {
                const codAeroportoOrigem = trecho.codAeroportoOrigem.codigo.split(" ")[0];
                const codAeroportoDestino = trecho.codAeroportoDestino.codigo.split(" ")[0];

                return {
                    codAeroportoOrigem,
                    codAeroportoDestino,
                    dataEmbarque: trecho.dataEmbarque,
                };
            });
        }

      this.availabilityService.availabilitySearch(values, xmls);

      this.availabilityService.response.subscribe((value) => {

        const data = value.body ? value.body : value.data;
        
        if (value.status == undefined) {
          this.loadingSearch = true;
        } else {
          if (value.status === HttpStatusCode.Ok) {
            const id = data.idBusca;
            this.filterId.setValue(id);
            this.availabilityService.setLastSearchId(id);
            const hasIdConfigBusca = data.dadosBusca.idConfigBusca !== undefined;
            const hasXmls = data.xmls ? data.xmls.length > this.EMPTY : false;
            const hasIdConfigBuscaOrXmls = hasIdConfigBusca || hasXmls;            

            if(hasIdConfigBusca) {
              localStorage.setItem('lastSearchResponse', JSON.stringify({dadosBusca: data.dadosBusca}));
            } else {
              this.availabilityService.loadFilterById(id).then(response => {
                this.availabilityService.storeLastLocalSearch(id, response);
              });
            }
            
            this.availabilityService.setHasIdConfigBuscaOrXmls(hasIdConfigBuscaOrXmls);
            this.hasLastSearchResponse = hasIdConfigBuscaOrXmls;
            this.router.navigateByUrl(`availabilitySearch/result`);
          }
          this.loadingSearch = false;
          this.response = value;
        }
      });
    } else {
      this.availabilityFormGroup.markAllAsTouched();
      this.snackBar.openFromComponent(SnackBarComponent, {
        data: {
          title: 'Alerta',
          subTitle: 'Verifique se todos os campos estão preenchidos corretamente',
          buttons: [{label: 'Fechar', action: 'close', color: 'warn'}]
        }
      });
    }
  }

  loadLastSearchResults() {
    const lastSearchResponse = localStorage.getItem('lastSearchResponse');
    if (lastSearchResponse) {
      const parsedResponse = JSON.parse(lastSearchResponse);
      const id = parsedResponse.idBusca;
      this.filterId.setValue(id);
      this.availabilityService.setLastSearchId(id);

      this.router.navigate([`availabilitySearch/result`]).then(() => {
      }).catch(error => {
        this.hasLastSearchResponse = false;
        this.router.navigate([`availabilitySearch/`]);
      });
    } else {
      this.snackBar.openFromComponent(SnackBarComponent, {
        data: {
          title: 'Alerta',
          subTitle: 'Nenhum resultado de busca anterior encontrado',
          buttons: [{ label: 'Fechar', action: 'close', color: 'warn' }]
        }
      });
    }
  }

  resetForm() {
    this.availabilityFormGroup.reset();
    this.shouldResetSearchBoxForm = true;
  }

  searchConfigs() {
    if (this.availabilityFormGroup.valid && this.loadingSearch === false) {
      this.loadingSearch = true;
      const values = this.availabilityFormGroup.value;
      if (values.trechos[0].codAeroportoOrigem.codigo != null) {
        values.trechos = values.trechos.map((trecho) => {
            const codAeroportoOrigem = trecho.codAeroportoOrigem.codigo.split(" ")[0];
            const codAeroportoDestino = trecho.codAeroportoDestino.codigo.split(" ")[0];

            return {
                codAeroportoOrigem,
                codAeroportoDestino,
                dataEmbarque: trecho.dataEmbarque,
            };
        });
    }
      this.availabilityService.searchConfig(values).then(value => {
        this.loadingSearch = false;
        this.showDebugDialog(value ? value.data : []);
      }).catch(err => {
        this.loadingSearch = false;
        throw err;
      });
    }
  }

  public showDebugDialog(data: any): void {
    const dialogRef = this.dialog.open(SearchConfigDialogComponent, {
      minHeight: '550px',
      maxHeight: '750px',
      width: '850px',
      data,
    });
  }

  public isAgenciaRequired(): boolean {
    const usuario = this.availabilityFormGroup.get('usuario')?.value;
    const empresa = usuario?.empresa;
    return empresa?.perfil === PERFIL_EMPRESA_INTEGRACAO;
  }

  public isAgenciaInvalid(): boolean {
    const usuario = this.availabilityFormGroup.get('usuario')?.value;
    const filial = this.availabilityFormGroup.get('filial')?.value;
    const agencia = this.availabilityFormGroup.get('agencia')?.value;
    const empresa = usuario?.empresa;

    if(agencia === null || agencia === undefined || filial === null || filial === undefined) {
      return false;
    }

    if(this.isAgenciaRequired()) {
      const isDiffFilial = agencia?.filial?.id !== filial?.id;
      const isDiffEmpresa = agencia?.filial?.empresa?.id !== empresa?.id;
      const isAgenciaInvalid = isDiffFilial || isDiffEmpresa;

      if(isAgenciaInvalid) {

        if(isDiffFilial) {
          this.invalidAgenciaLabel = `A agência '${agencia?.nomeFantasia}' não está associada a filial '${filial?.nome}'.`;
        } else if (isDiffEmpresa) {
          this.invalidAgenciaLabel = `A filial '${filial?.nome}' não está associada a empresa do usuário.`;
        }
      }

      return isAgenciaInvalid;
    }
  }

  showGatewayResponse() {
    if (this.availabilityFormGroup.valid && this.loadingSearch === false) {
      this.loadingSearch = true;
      let values = this.availabilityFormGroup.value;
      values.cabine = values.cabine || 'UNDEF';

      values = Object.keys(values).reduce((acc, key) => {
        if (values[key] !== null) {
          acc[key] = values[key];
        }
        return acc;
      }, {});

      if (values.trechos[0].codAeroportoOrigem.codigo != null) {
        values.trechos = values.trechos.map((trecho) => {
          const codAeroportoOrigem = trecho.codAeroportoOrigem.codigo.split(" ")[0];
          const codAeroportoDestino = trecho.codAeroportoDestino.codigo.split(" ")[0];

          return {
            codAeroportoOrigem,
            codAeroportoDestino,
            dataEmbarque: trecho.dataEmbarque,
          };
        });
      }

      // Fazer a chamada com xmls = false para o botão Resposta GATEWAY
      this.availabilityService.availabilitySearch(values, false);

      this.availabilityService.response.subscribe(value => {
        if (value && value.data) {
          const hasIdConfigBuscaOrXmls = value.data.idBusca || value.data.dadosBusca?.idConfigBusca || value.data.xmls;

          if (hasIdConfigBuscaOrXmls) {
            localStorage.setItem('lastSearchResponse', JSON.stringify(value.data));
            this.availabilityService.setHasIdConfigBuscaOrXmls(hasIdConfigBuscaOrXmls);
            this.hasLastSearchResponse = hasIdConfigBuscaOrXmls;
            // Navegar para a tela de gateway em vez da tela de resultado normal
            this.router.navigateByUrl(`availabilitySearch/gateway`);
          }
          this.loadingSearch = false;
          this.response = value;
        }
      });
    } else {
      this.availabilityFormGroup.markAllAsTouched();
      this.snackBar.openFromComponent(SnackBarComponent, {
        data: {
          title: 'Alerta',
          subTitle: 'Verifique se todos os campos estão preenchidos corretamente',
          buttons: [{label: 'Fechar', action: 'close', color: 'warn'}]
        }
      });
    }
  }
}
