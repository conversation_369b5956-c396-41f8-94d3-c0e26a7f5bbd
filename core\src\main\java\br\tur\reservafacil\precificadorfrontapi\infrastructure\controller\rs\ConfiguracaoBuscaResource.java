package br.tur.reservafacil.precificadorfrontapi.infrastructure.controller.rs;

import br.tur.reservafacil.dominio.aereo.ConfiguracaoBusca;
import br.tur.reservafacil.dominio.aereo.common.IdList;
import br.tur.reservafacil.dominio.aereo.historico.HistoricoConfiguracaoBusca;
import br.tur.reservafacil.dominio.aereo.v0.to.common.MensagemResponse;
import br.tur.reservafacil.precificadorfrontapi.application.service.ConfiguracaoBuscaAcordoComercialService;
import br.tur.reservafacil.precificadorfrontapi.domain.component.restricao.RestricaoTreeComponent;

import br.tur.reservafacil.dominio.aereo.Restricao;
import br.tur.reservafacil.precificadorfrontapi.application.service.ConfiguracaoBuscaService;
import br.tur.reservafacil.precificadorfrontapi.application.service.historico.HistoricoConfiguracaoBuscaService;
import br.tur.reservafacil.precificadorfrontapi.domain.exception.IntegridadeVioladaException;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.controller.rs.common.ObtemFiltrosComponent;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.controller.rs.view.to.ConfiguracaoBuscaView;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.controller.rs.view.to.RestricaoView;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.controller.rs.view.to.edicaoEmMassa.busca.ConfiguracaoBuscaEdicaoEmMassa;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.controller.util.ResponseEntityUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.ValidationException;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * Created by davidson on 6/3/16.
 */
@RestController
@RequestMapping("/rs/v1/configuracoesBusca")
public class ConfiguracaoBuscaResource extends AbstractVersionavelViewResource<ConfiguracaoBuscaView, ConfiguracaoBusca, Integer, ConfiguracaoBuscaService> {

	private static final String FILE_NAME = "configuracaoBusca.xls";
	private final RestricaoTreeComponent restricaoTreeComponent;
	private final HistoricoConfiguracaoBuscaService historicoService;


	@Autowired
	public ConfiguracaoBuscaResource(ConfiguracaoBuscaService service, RestricaoTreeComponent restricaoTreeComponent, HistoricoConfiguracaoBuscaService historicoService, ConfiguracaoBuscaAcordoComercialService configuracaoBuscaAcordoComercialService) {
		super(service);
		this.restricaoTreeComponent = restricaoTreeComponent;
		this.historicoService = historicoService;
    }


	@Override
	protected ConfiguracaoBuscaView transformToView(ConfiguracaoBusca model) {
		if (model != null) {
			Integer idConfigEditavel;
			if (!model.isEditavel()) {
				idConfigEditavel = this.service.ultimoIdEditavelPorIdOrigem(model.getIdOrigem());
			} else {
				idConfigEditavel = model.getId();
			}
			return new ConfiguracaoBuscaView(model, idConfigEditavel, model.getRestricoes());
		}
		return null;
	}

	@Override protected ConfiguracaoBusca transformToModel(ConfiguracaoBuscaView view) {
		if (view != null) {
			return view.getModel();
		}
		return null;
	}

	@RequestMapping(value = "/relatorio", method = RequestMethod.GET, produces = "application/vnd.ms-excel") public ResponseEntity<?> criaRelatorio(
			@RequestParam Map<String, String> params) {
		this.removeFieldsPageNumberPageLimitsAndOrderBy(params);
		final Resource resource = service.getRelatorioConfiguracao(params);
		return ResponseEntityUtil.downloadEntity(resource, FILE_NAME);
	}

	@Override
	public ResponseEntity<ConfiguracaoBuscaView> findById(@PathVariable Integer id,
														  @RequestHeader(value = FIELDS_HEADER_RQ, required = false) String fieldsHeader,
														  @RequestParam(value = FIELDS_QUERY_RQ, required = false) String fieldsQuery) {
		String fields = ObtemFiltrosComponent.obterFiltros(fieldsHeader, fieldsQuery);

		ConfiguracaoBusca configuracaoBusca = this.service.findByPkEager(id);

		configuracaoBusca.setRestricoes(configuracaoBusca.getRestricoes().stream().sorted(Comparator.comparing(Restricao::getId)).collect(Collectors.toList()));

		configuracaoBusca.setRestricoes(restricaoTreeComponent.gerarArvoreDeRestricoes(configuracaoBusca.getRestricoes()));

		ConfiguracaoBuscaView configuracaoBuscaView = this.transformToView(configuracaoBusca);

		configuracaoBuscaView.setHistorico(this.historicoService
				.buscaHistoricoByIdOrigem(configuracaoBuscaView.getIdOrigem())
				.stream()
				.peek(historico -> setaAuditoria(historico, configuracaoBuscaView))
				.filter(historico -> !configuracaoBuscaView.getId().equals(historico.getConfigBusca().getId()))
				.sorted(Comparator.comparing(HistoricoConfiguracaoBusca::getId).reversed())
				.collect(Collectors.toList()));

		return this.filterResponseFields(ResponseEntity.ok(configuracaoBuscaView), fields);
	}

    @Override protected ResponseEntity<List<ConfiguracaoBuscaView>> doGet(String fields, int page, Integer pageSize, String orderBy,
                                                                          boolean useOrToFind, Map<String, String> params)
	    throws IllegalAccessException {
        params = this.convertParms(params);
	List<ConfiguracaoBusca> models = this.service.findByParameters(page, pageSize, orderBy, params);
	List<ConfiguracaoBuscaView> views = models.stream().map(c -> new ConfiguracaoBuscaView(c, restricaoTreeComponent.gerarArvoreDeRestricoes(c.getRestricoes()))).collect(Collectors.toList());
	Double qtdTotalItens = params.isEmpty() ? Double.valueOf(this.service.countAll()) : Double.valueOf(this.service.countByParameters(params));
	Double qtdTotalPages = pageSize == null ? 1 : Math.ceil(qtdTotalItens / pageSize);
	HttpHeaders responseHeaders = createHttpHeaders(page, qtdTotalPages.intValue(), qtdTotalItens.intValue());
	HttpStatus httpStatus = createHttpStatus(models);

	return this.filterResponseFields(new ResponseEntity<>(views, responseHeaders, httpStatus), fields);
    }

    private Map<String, String> convertParms(Map<String, String> map) {
	for (Map.Entry<String, String> entry : map.entrySet()) {
	    String key = entry.getKey();
	    String value = entry.getValue();
	    if (value != null) {
	        value = value.replaceAll("!!plus", "+");
		map.put(key, value);
	    }
	}
	return map;
    }

    private void setaAuditoria(HistoricoConfiguracaoBusca historico, ConfiguracaoBuscaView configuracaoBuscaView) {
        if (configuracaoBuscaView.getId().equals(historico.getConfigBusca().getId())) {
            configuracaoBuscaView.setAuditoria(historico);
        }
    }

    @RequestMapping(value = "/{idConfBusca}/restricoes", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public ResponseEntity<List<RestricaoView>> findRestricoesByConfiguracaoBusca(@PathVariable Integer idConfBusca,
																				 @RequestHeader(value = FIELDS_HEADER_RQ, required = false) String fieldsHeader,
																				 @RequestParam(value = FIELDS_QUERY_RQ, required = false) String fieldsQuery) {
		String fields = ObtemFiltrosComponent.obterFiltros(fieldsHeader, fieldsQuery);
		return this.filterResponseFields(ResponseEntity.ok(this.transformToView(this.service.findRestricoesByConfiguracaoBusca(idConfBusca))), fields);
	}

	@RequestMapping(value = "/{idConfBusca}/publicar", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_UTF8_VALUE) public ResponseEntity<ConfiguracaoBuscaView> publicaConfiguracao(
			@PathVariable Integer idConfBusca) {
		return ResponseEntity.ok(this.transformToView(this.service.publicaConfiguracao(idConfBusca)));
	}

    @RequestMapping(value = "/{idConfBusca}/clonar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ResponseEntity<ConfiguracaoBuscaView> clonaConfiguracao(@PathVariable Integer idConfBusca,
                                                                   @RequestHeader(value = FIELDS_HEADER_RQ, required = false) String fieldsHeader,
                                                                   @RequestParam(value = FIELDS_QUERY_RQ, required = false) String fieldsQuery) {
	String fields = ObtemFiltrosComponent.obterFiltros(fieldsHeader, fieldsQuery);
	return this.filterResponseFields(ResponseEntity.ok(this.transformToView(this.service.clonaConfiguracao(idConfBusca))), fields);
    }


	@RequestMapping(value = "/editarPesos", consumes = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.PUT)
	@ResponseBody
	public ResponseEntity<?> edicaoEmMassa(@RequestBody ConfiguracaoBuscaEdicaoEmMassa paramEdicaoEmMassa) {
		try {
			this.service.editarEmMassa(paramEdicaoEmMassa);
			return ResponseEntity.status(HttpStatus.OK).build();
		} catch (ValidationException e) {
			LOG.error("Erro realizando edição em massa!", e);
			return new ResponseEntity<>(new MensagemResponse("Não foi possível salvar! " + e.getMessage()), HttpStatus.BAD_REQUEST);
		} catch (IntegridadeVioladaException e) {
			LOG.error(e.getMessage());
			MensagemResponse mensagemResponse = getMensagemResponse(e);
			return new ResponseEntity<>(mensagemResponse, HttpStatus.BAD_REQUEST);
		} catch (Exception e1) {
			LOG.error("Erro realizando edição em massa!", e1);
			return new ResponseEntity<>(new MensagemResponse("Não foi possível salvar! " + e1.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/publicar", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE) public ResponseEntity<IdList> publicaConfiguracao(
			@RequestBody IdList ids) {
		return ResponseEntity.ok(this.service.publicaConfiguracao(ids.getIds()));
	}

	@RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
	@ResponseBody
	public ResponseEntity<?> delete(@PathVariable Integer id,
									@RequestHeader(value = FIELDS_HEADER_RQ, required = false) String fieldsHeader,
									@RequestParam(value = FIELDS_QUERY_RQ, required = false) String fieldsQuery) {
		try {
			ResponseEntity<ConfiguracaoBuscaView> configuracaoBuscaView = this.findById(id, fieldsHeader, fieldsQuery);

			this.service.inativarRegistro(id);

			return configuracaoBuscaView;
		} catch (ValidationException e) {
			LOG.error(e.getMessage());
			return new ResponseEntity<>(new MensagemResponse("Não foi possível inativar o registro! " + e.getMessage()), HttpStatus.BAD_REQUEST);
		}
	}
    @RequestMapping(value = "/cancelAll", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> deleteMultiple(@RequestBody List<Integer> ids) {
	try {
	    for (Integer id : ids) {
		this.service.inativarRegistro(id);
	    }
	    return ResponseEntity.ok().build();
	} catch (ValidationException e) {
	    LOG.error(e.getMessage());
	    return new ResponseEntity<>(new MensagemResponse("Não foi possível inativar os registros! " + e.getMessage()), HttpStatus.BAD_REQUEST);
	}
    }

	protected List<RestricaoView> transformToView(List<Restricao> restricoes) {
		return CollectionUtils.isNotEmpty(restricoes) ?
				restricoes.stream().map(RestricaoView::new).collect(Collectors.toList()) : Collections.emptyList();
	}

}
