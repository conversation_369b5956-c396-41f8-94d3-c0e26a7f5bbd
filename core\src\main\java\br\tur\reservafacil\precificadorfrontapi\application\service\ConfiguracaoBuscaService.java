package br.tur.reservafacil.precificadorfrontapi.application.service;

import br.tur.reservafacil.dominio.Agencia;
import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.Filial;
import br.tur.reservafacil.dominio.Grupo;
import br.tur.reservafacil.dominio.aereo.AcordoComercial;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoBusca;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoBuscaAcordoComercial;
import br.tur.reservafacil.dominio.aereo.Restricao;
import br.tur.reservafacil.dominio.aereo.common.IdList;
import br.tur.reservafacil.dominio.tipo.SistEmis;
import br.tur.reservafacil.dominio.tipo.TipoOperador;
import br.tur.reservafacil.dominio.tipo.TipoRestricao;
import br.tur.reservafacil.precificadorfrontapi.application.service.publicadores.PublicadorConfiguracaoBuscaService;
import br.tur.reservafacil.precificadorfrontapi.application.service.report.configuracaoBusca.*;
import br.tur.reservafacil.precificadorfrontapi.domain.component.configuracaoBusca.ClonadorConfiguracaoBuscaComponent;
import br.tur.reservafacil.precificadorfrontapi.domain.component.configuracaoBusca.edicaoEmMassa.ExecutorEdicaoEmMassaConfiguracaoBusca;
import br.tur.reservafacil.precificadorfrontapi.domain.component.relatorio.RelatorioGeneratorComponent;
import br.tur.reservafacil.precificadorfrontapi.domain.component.restricao.RestricaoTreeComponent;
import br.tur.reservafacil.precificadorfrontapi.domain.component.restricao.RestricaoValidatorComponent;
import br.tur.reservafacil.precificadorfrontapi.domain.exception.IntegridadeVioladaException;
import br.tur.reservafacil.precificadorfrontapi.domain.repository.*;
import br.tur.reservafacil.precificadorfrontapi.domain.strategy.restricao.operacao.AssinaturaOperacao;
import br.tur.reservafacil.precificadorfrontapi.domain.strategy.restricao.operacao.OperacaoFactory;
import br.tur.reservafacil.precificadorfrontapi.domain.strategy.restricao.operacao.OperacaoFactoryDefaultImpl;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.controller.rs.view.to.edicaoEmMassa.busca.ConfiguracaoBuscaEdicaoEmMassa;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.database.jooq.dao.AgenciaJooqDao;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.database.jooq.dao.FilialJooqDao;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.database.jooq.dao.GrupoJooqDao;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.database.jooq.dao.UsuarioJooqDao;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.poi.component.WorkbookGenerator;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.report.builder.BasicReportBuilder;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.report.elementos.Relatorio;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.util.Pageable;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.util.PaginadorWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.DSLContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static br.tur.reservafacil.dominio.tipo.TipoRestricao.*;
import static br.tur.reservafacil.precificadorfrontapi.application.service.report.comum.RestricaoDataCellValueResolver.DataInicioFim.FIM;
import static br.tur.reservafacil.precificadorfrontapi.application.service.report.comum.RestricaoDataCellValueResolver.DataInicioFim.INICIO;
import static br.tur.reservafacil.precificadorfrontapi.infrastructure.database.jooq.dao.ConfiguracaoBuscaJooqDao.*;
import static java.util.Arrays.asList;

/**
 * Created by davidson on 6/3/16.
 */
@Service
public class ConfiguracaoBuscaService extends AbstractConfiguracaoVersionavelService<ConfiguracaoBusca, Integer, ConfiguracaoBuscaRepository> {


    private static final Logger LOG = LoggerFactory.getLogger(ConfiguracaoBuscaService.class);

    @Autowired
    public UsuarioJooqDao usuarioRepository;

    @Autowired
    public WorkbookGenerator workbookGenerator;

    @Autowired
    private ClonadorConfiguracaoBuscaComponent clonadorConfComponent;

    @Autowired
    private PublicadorConfiguracaoBuscaService publicadorConfComponent;

    @Autowired
    private AcordoComercialService acordoComercialService;

    @Autowired
    private RelatorioGeneratorComponent reportGenerator;

    @Autowired
    private EmpresaRepository empresaRepository;

    @Autowired
    private FilialRepository filialRepository;

    @Autowired
    private AgenciaRepository agenciaRepository;

    @Autowired
    private GrupoRepository grupoRepository;

    @Autowired
    private RestricaoValidatorComponent restricaoValidatorComponent;

    @Autowired
    private ExecutorEdicaoEmMassaConfiguracaoBusca executorEdicaoEmMassa;

    @Autowired
    private RestricaoTreeComponent restricaoTreeComponent;

	@Autowired
	private DSLContext context;

    private OperacaoFactory<AssinaturaOperacao> operacaoFactory = new OperacaoFactoryDefaultImpl();

    /**
     * @param value a configuração a ser atualizada
     * @return a configuração enviada para atualização já atualizada quando o status da mesma for diferente de PUBLICADA,
     * ou retorna uma nova configuração com status NAO_PUBLICADA para que as modificações sejam testadas antes de ir para o ambiente de produção
     */
    @Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = {RuntimeException.class, NullPointerException.class, IntegridadeVioladaException.class})
    public ConfiguracaoBusca update(ConfiguracaoBusca value) {
	if (value.getStatusPublicacao().isPublicada()) {
		ConfiguracaoBusca confOriginal = this.findByKey(value.getId());
	    ConfiguracaoBusca confClonada = this.clonadorConfComponent.clonar(value, true);
		confClonada.setIdOrigem(confOriginal.getIdOrigem());
		// Try to avoid constraint [Nome+Versao] clashes for same idOrigem
		confClonada.setVersao(this.getRepository().getCurrentVersionByIdOrigem(confClonada.getIdOrigem()) + 1);
		confOriginal.setEditavel(false);

		this.getRepository().updateOne(confOriginal);
		this.insert(confClonada);
	    return confClonada;
	}
	super.update(value);
	return value;
    }

    /**
     * Efetiva a configuração informada para o ambiente de produção, mudando o status da configuração de origem para 'NÃO PUBLICADA' e o da informada para 'PUBLICADA'.
     * Alem de gerar o histórico de de modificação de configuração.
     *
     * @param idConfiguracao o id da configuração a ser enviada para o ambiente de produção
     * @return a configuração com o status PUBLICADA
     */
    public ConfiguracaoBusca publicaConfiguracao(Integer idConfiguracao) {
	ConfiguracaoBusca value = this.findByPkEager(idConfiguracao);
	return this.publicadorConfComponent.publicaConfiguracao(value);
    }

    public IdList publicaConfiguracao(List<Integer> idsConfiguracoes) {
	IdList idList = new IdList();
	for (Integer id : idsConfiguracoes) {
	    idList.getIds().add(this.publicaConfiguracao(id).getId());
	}
	return idList;
    }

    /**
     * Clona a configuração passada para ser salva com um novo ID e sem ligação com a anterior
     *
     * @param idConfiguracao o id da configuração a ser clonada
     * @return uma nova configuração copia da passada pronta para ser salva
     */
    public ConfiguracaoBusca clonaConfiguracao(Integer idConfiguracao) {
	ConfiguracaoBusca value = this.findByPkEager(idConfiguracao);
	return this.clonadorConfComponent.clonar(value, false);
    }

    public Resource getRelatorioConfiguracao(Map<String, String> params) {
	LOG.info("Gerando relatório de configuração de busca");

	List<ConfiguracaoBusca> configuracoes = this.getRepository().findEagerByParameters(findParamsIdByParamsName(params));

	Relatorio relatorio = criarRelatorio(configuracoes);

	return reportGenerator.createResource(relatorio);
    }
    public List<ConfiguracaoBusca> findByParameters(int page, Integer pageSize, String orderBy, Map<String, String> params) {
	List<ConfiguracaoBusca> resultados = this.getRepository().findByParameters(page, pageSize, orderBy, findParamsIdByParamsName(params));
	resultados.forEach(ConfiguracaoBusca::agrupaRestricoes);
	return resultados;
    }

    public List<ConfiguracaoBusca> findByKeys(Set<Integer> ids) {
	return this.getRepository().findByKeys(ids);
    }

    private Relatorio criarRelatorio(List<ConfiguracaoBusca> configuracoes) {
	List<ConfiguracaoBuscaAcordoComercial>
			configAcordo =
			configuracoes.stream().flatMap(config -> config.getAcordosComerciais().stream().map(
					acordo -> new ConfiguracaoBuscaAcordoComercial(acordo, config))).collect(Collectors.toList());

	return 	new BasicReportBuilder()
			.setRecords(configAcordo)
			.addColumn("Configuração de Busca", "configuracaoBusca.nome")
			.addColumn("Prioridade", "configuracaoBusca.prioridade")
			.addColumn("Empresa", "configuracaoBusca.empresa.nome")
			.addColumn("Status", "configuracaoBusca.statusPublicacao")
			.addColumn("Nacional/Internacional", "configuracaoBusca.nacInt")
			.addColumn("Acordo", "acordoComercial.nome")
			.addColumn("Tipo de Tarifa", "acordoComercial.tipoTarifaAcordo")
			.addColumn("Código de Contrato", "acordoComercial.codigoContrato.codigo")
			.addColumn("PTC", new ConfigBuscaPTCCellValueResolver())
			.addColumn("Produtos", new ConfigBuscaProdutosCellValueResolver())
			.addColumn("Filiais", new ConfigBuscaFiliaisCellValueResolver())
			.addColumn("Código Cia", new ConfigBuscaCiasCellValueResolver())
			.addColumn("Restrições", new ConfigBuscaRestricoesCellValueResolver())

			.addColumn("Data Venda Início", new ConfigBuscaRestricaoDataCellValueResolver(DATA_VENDA, INICIO))
			.addColumn("Data Venda Fim", new ConfigBuscaRestricaoDataCellValueResolver(DATA_VENDA, FIM))

			.addColumn("Data Embarque Início", new ConfigBuscaRestricaoDataCellValueResolver(DATA_EMBARQUE, INICIO))
			.addColumn("Data Embarque Fim", new ConfigBuscaRestricaoDataCellValueResolver(DATA_EMBARQUE, FIM))

			.addColumn("Origem (Igual)", new ConfigBuscaRestricaoEspecificaCellValueResolver(asList(AEROPORTO_ORIGEM, CIDADE_ORIGEM), TipoOperador.IGUAL))
			.addColumn("Origem (Diferente)", new ConfigBuscaRestricaoEspecificaCellValueResolver(asList(AEROPORTO_ORIGEM, CIDADE_ORIGEM), TipoOperador.DIFERENTE))

			.addColumn("Destino (Igual)", new ConfigBuscaRestricaoEspecificaCellValueResolver(asList(AEROPORTO_DESTINO_TODOS_DESTINOS, CIDADE_DESTINO_TODOS_DESTINOS), TipoOperador.IGUAL))
			.addColumn("Destino (Diferente)", new ConfigBuscaRestricaoEspecificaCellValueResolver(asList(AEROPORTO_DESTINO_TODOS_DESTINOS, CIDADE_DESTINO_TODOS_DESTINOS), TipoOperador.DIFERENTE))

			.addColumn("Ativo", "configuracaoBusca.ativo")
			.setSheetName("Configuracao de Busca").buildReport();
    }

    public ConfiguracaoBusca findByPkEager(Integer idConfBusca) {
	final ConfiguracaoBusca configuracaoBusca = this.getRepository().findByPkEager(idConfBusca);
//	configuracaoBusca.agrupaRestricoes();
	return configuracaoBusca;
    }

    public List<Restricao> findRestricoesByConfiguracaoBusca(Integer idConfBusca) {
	return this.findByPkEager(idConfBusca).getRestricoes();
    }

    public List<ConfiguracaoBusca> findEagerByKeys(Set<Integer> keys) {
	List<ConfiguracaoBusca> configuracoes = getRepository().findEagerByKeys(keys);
	configuracoes.forEach(ConfiguracaoBusca::agrupaRestricoes);
	return configuracoes;
    }


    private Map<String, String> findParamsIdByParamsName(Map<String, String> params) {
	//comentado os codigos abaixo para o filtro ser feito sem nenhuma relacao entre si ( grupo -> agencia -> empresa )
        Grupo grupo = null;
        if (params.containsKey(PARAM_GRUPO)) {
	    PaginadorWrapper<Grupo> grupoPaginadorWrapper = this.grupoRepository.findByParameters(
			    Collections.singletonMap(GrupoJooqDao.PARAM_ID, params.get(PARAM_GRUPO)),
			    new Pageable(1, 1));
	    if (CollectionUtils.isNotEmpty(grupoPaginadorWrapper.getElementos())) {
	        grupo = grupoPaginadorWrapper.getElementos().get(0);
		params.put(PARAM_GRUPO_ID, String.valueOf(grupo.getId()));
	    }
        }

        Agencia agencia = null;
//        if (grupo != null) {
//            params.put(PARAM_GRUPO_ID, grupo.getId().toString());
//            agencia = this.agenciaRepository.findEagerByPk(grupo.getAgencia().getId()).orElse(null);
//        } else
	if (params.containsKey(PARAM_AGENCIA)) {
	    List<Agencia> agencias = this.agenciaRepository.findByParameters(Collections.singletonMap(AgenciaJooqDao.PARAM_NOME, params.get(PARAM_AGENCIA)), 1, 1);
	    if (CollectionUtils.isNotEmpty(agencias)) {
		agencia = agencias.get(0);
		params.put(PARAM_AGENCIA_ID, String.valueOf(agencia.getId()));
	    }
	}

        Filial filial = null;
	//removido para filtar
//        if (agencia != null) {
//	    filial = agencia.getFilial();
//	    params.put(PARAM_AGENCIA_ID, agencia.getId().toString());
//        } else
	if (params.containsKey(PARAM_FILIAL)) {
	    PaginadorWrapper<Filial> filialPaginadorWrapper = this.filialRepository.findByParameters(Collections.singletonMap(FilialJooqDao.PARAM_NOME, params.get(PARAM_FILIAL)), new Pageable(1, 1));
	    if (CollectionUtils.isNotEmpty(filialPaginadorWrapper.getElementos())) {
		filial = filialPaginadorWrapper.getElementos().get(0);
		params.put(PARAM_FILIAL_ID, String.valueOf(filial.getId()));
	    }
	}
//        }

        List<Empresa> empresas = new ArrayList<>();
//        if (filial != null) {
//	    empresas.add(filial.getEmpresa());
//	    params.put(PARAM_FILIAL_ID, filial.getId().toString());
//        } else
	    if (params.containsKey(PARAM_EMPRESA)) {
            if (params.get(PARAM_EMPRESA).contains(",")) {
                empresas.addAll(Arrays.stream(params.get(PARAM_EMPRESA).split(","))
		                                .map(empresa -> this.empresaRepository.buscaEmpresaPorNome(empresa))
		                                .filter(Objects::nonNull)
		                                .collect(Collectors.toList()));
            } else {
                empresas.add(this.empresaRepository.buscaEmpresaPorNome(params.get(PARAM_EMPRESA)));
            }
        }
        if (CollectionUtils.isNotEmpty(empresas)) {
	    params.put(PARAM_EMPRESA_ID, empresas.stream().map(Empresa::getId).map(Objects::toString).collect(Collectors.joining(",")));
        }

        return params;
    }

    public Integer countByParameters(Map<String, String> params) {
	return this.getRepository().countByParameters(params);
    }

    @Transactional
    public void editarEmMassa(ConfiguracaoBuscaEdicaoEmMassa paramEdicaoEmMassa) {
	final List<ConfiguracaoBusca> configuracoes = this.getRepository().findEagerByKeys(new HashSet<>(paramEdicaoEmMassa.getIds()));

	final List<ConfiguracaoBusca> configuracoesEditadas = this.executorEdicaoEmMassa.editar(configuracoes, paramEdicaoEmMassa.getOperacoes());

	configuracoesEditadas.stream().forEach(this::update);
    }

    public List<AcordoComercial> findAcordosByConfiguracaoId(Integer idConfiguracao, List<String> codigoCias,
															 List<SistEmis> sistemasEmissores) {
	return acordoComercialService.findAcordosVigentesByConfiguracaoId(idConfiguracao, codigoCias, sistemasEmissores);
    }

    public List<Integer> findIdsDeConfiguracoesDisponiveis(String grupoRef, String empresaRef) {
	final Empresa empresa = empresaRepository.buscaEmpresaPorReferencia(empresaRef);

	if (empresa == null) {
	    return Collections.emptyList();
	}

	final Filial filial = filialRepository.buscarPorReferenciaEEmpresa(grupoRef, empresa.getId());

	if (filial == null) {
	    return Collections.emptyList();
	}

	List<ConfiguracaoBusca> configuracoesBusca = this.getRepository().findConfiguracaoSoComRestricaoByEmpresa(empresa);

	if (CollectionUtils.isEmpty(configuracoesBusca)) {
	    return Collections.emptyList();
	}

	final String strFilialId = filial.getId().toString();
	List<Restricao> restricaoFilial = Arrays.asList(new Restricao(strFilialId, TipoOperador.IGUAL, TipoRestricao.FILIAL));

	final List<Restricao> restricoes = configuracoesBusca.stream().flatMap(c -> c.getRestricoes().stream())
			.filter(r -> r.getTipoRestricao() == TipoRestricao.FILIAL)
			.collect(Collectors.toList());

	List<ConfiguracaoBusca> configuracoesBuscasValidas = new ArrayList<>();

	if (CollectionUtils.isEmpty(restricoes)) {
	    configuracoesBuscasValidas = configuracoesBusca;
	} else {
	    configuracoesBusca.stream()
			    .filter(c -> restricaoValidatorComponent.validaRestricoes(restricaoFilial, c.getRestricoes()))
			    .forEach(configuracoesBuscasValidas::add);
	}

	return configuracoesBuscasValidas.stream().map(ConfiguracaoBusca::getId).collect(Collectors.toList());
    }

    @Override
    public ConfiguracaoBusca findByKey(Integer key) {
        ConfiguracaoBusca configuracaoBusca = this.getRepository().findByPkEager(key);
        configuracaoBusca.setRestricoes(restricaoTreeComponent.gerarArvoreDeRestricoes(configuracaoBusca.getRestricoes()));
	return configuracaoBusca;
    }

}
