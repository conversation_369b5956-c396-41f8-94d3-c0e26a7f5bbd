<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>precificador-front-api</artifactId>
        <groupId>br.tur.reservafacil.soa</groupId>
        <version>2.13.470-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <packaging>jar</packaging>
    <artifactId>core</artifactId>

    <build>
        <finalName>${artifactIdForWarFile}##${git.commitsCount}-${project.version}</finalName>
        <directory>../target</directory>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>2.10.3</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                    <configuration>
                        <skip>true</skip>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.codehaus.cargo</groupId>
                <artifactId>cargo-maven2-plugin</artifactId>
                <version>1.4.8</version>
                <configuration>
                    <container>
                        <containerId>jetty8x</containerId>
                        <type>embedded</type>
                        <timeout>300000</timeout>
                        <systemProperties>
                            <spring.profiles.active>integration-test</spring.profiles.active>
                        </systemProperties>
                    </container>
                    <configuration>
                        <properties>
                            <cargo.servlet.port>8481</cargo.servlet.port>
                            <cargo.jvmargs>-Xss10m -Xms1024M -Xmx2048M
                                -agentlib:jdwp=transport=dt_socket,address=8481,server=y,suspend=n
                            </cargo.jvmargs>
                        </properties>
                    </configuration>
                </configuration>
            </plugin>

            <!-- Configuração de testes unitários. Importante quando forem configurados testes de inetgração -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <includes>
                        <include>**/*.java</include>
                    </includes>
                    <excludes>
                        <exclude>**/*IntegrationTest.java</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>ru.concerteza.buildnumber</groupId>
                <artifactId>maven-jgit-buildnumber-plugin</artifactId>
                <version>1.2.7</version>
                <executions>
                    <execution>
                        <id>jgit-buildnumber</id>
                        <goals>
                            <goal>extract-buildnumber</goal>
                        </goals>
                        <phase>validate</phase>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.7</version>
                <executions>
                    <execution>
                        <id>echo-properties</id>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <phase>validate</phase>
                        <configuration>
                            <target>
                                <echo message="buildnumber-maven-plugin properties:"/>
                                <echo message=" $${scmBranch}: ${scmBranch}"/>
                                <echo message=" $${buildNumber}: ${buildNumber}"/>
                                <echo message=" $${timestamp}: ${timestamp}"/>

                                <echo message="maven-jgit-buildnumber-plugin properties:"/>
                                <echo message=" $${git.revision}: ${git.revision}"/>
                                <echo message=" $${git.branch}: ${git.branch}"/>
                                <echo message=" $${git.tag}: ${git.tag}"/>
                                <echo message=" $${git.commitsCount}: ${git.commitsCount}"/>
                                <echo message=" $${git.buildnumber}: ${git.buildnumber}"/>


                                <echo message="git-commit-id-plugin properties (aliased with git-commit-id):"/>
                                <echo message=" $${git.branch}: ${git-commit-id.branch}"/>

                                <echo message=" $${git.commit.id.describe}: ${git-commit-id.commit.id.describe}"/>

                                <echo message=" $${git.build.user.name}: ${git-commit-id.build.user.name}"/>
                                <echo message=" $${git.build.user.email}: ${git-commit-id.build.user.email}"/>
                                <echo message=" $${git.build.time}: ${git-commit-id.build.time}"/>

                                <echo message=" $${git.commit.id}: ${git-commit-id.commit.id}"/>
                                <echo message=" $${git.commit.id.abbrev}: ${git-commit-id.commit.id.abbrev}"/>
                                <echo message=" $${git.commit.user.name}: ${git-commit-id.commit.user.name}"/>
                                <echo message=" $${git.commit.user.email}: ${git-commit-id.commit.user.email}"/>
                                <echo message=" $${git.commit.message.full}: ${git-commit-id.commit.message.full}"/>
                                <echo message=" $${git.commit.message.short}: ${git-commit-id.commit.message.short}"/>
                                <echo message=" $${git.commit.time}: ${git-commit-id.commit.time}"/>

                            </target>
                        </configuration>
                    </execution>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <tasks>
                                <delete dir="home" includeemptydirs="true"/>
                                <concat append="yes" destfile="target/classes/build-scm.properties">
                                    git.commitsCount=${git.commitsCount}
                                </concat>
                            </tasks>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Compila testes Spock -->
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>1.5</version>
                <configuration>
                    <invokeDynamic>true</invokeDynamic>
                    <sourceEncoding>UTF-8</sourceEncoding>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>addSources</goal>
                            <goal>addTestSources</goal>
                            <goal>generateStubs</goal>
                            <goal>compile</goal>
                            <goal>testGenerateStubs</goal>
                            <goal>testCompile</goal>
                            <!--<goal>removeStubs</goal>-->
                            <!--<goal>removeTestStubs</goal>-->
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-all</artifactId>
                        <version>${groovy.version}</version>
                        <classifier>indy</classifier>
                    </dependency>
                </dependencies>
            </plugin>

            <plugin>
                <groupId>com.google.code.maven-replacer-plugin</groupId>
                <artifactId>replacer</artifactId>
                <version>1.5.1</version>
                <executions>
                    <execution>
                        <phase>initialize</phase>
                        <goals>
                            <goal>replace</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <file>kubernetes/minikube_jinja_template.json</file>
                    <outputFile>kubernetes/minikube_jinja.json</outputFile>
                    <replacements>
                        <replacement>
                            <token>("PRECIFICADOR_FRONT_API_IMAGE_VERSION":.*)</token>
                            <value>"PRECIFICADOR_FRONT_API_IMAGE_VERSION": "${project.version}",</value>
                        </replacement>
                        <replacement>
                            <token>@PROFILE@</token>
                            <value>${project.activeProfiles[0].id}</value>
                        </replacement>
                    </replacements>
                </configuration>
            </plugin>
            <plugin>
                <groupId>de.wintercloud</groupId>
                <artifactId>jinja-maven-plugin</artifactId>
                <version>1.0</version>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>renderjinja</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <outputFile>kubernetes/precificadorfrontapi.yaml</outputFile>
                    <templateFile>kubernetes/template.yaml.jinja</templateFile>
                    <varFile>kubernetes/minikube_jinja.json</varFile>
                </configuration>
            </plugin>
            <!-- Spring Boot <-> Maven Integration -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>build-info</goal>
                        </goals>
                        <configuration>
                            <additionalProperties>
                                <encoding.source>UTF-8</encoding.source>
                                <encoding.reporting>UTF-8</encoding.reporting>
                                <java.source>${maven.compiler.source}</java.source>
                                <java.target>${maven.compiler.target}</java.target>
                            </additionalProperties>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <dependencies>

        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>dominio</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>adapters</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>br.tur.reservafacil.soa</groupId>
            <artifactId>precificador-data</artifactId>
            <version>1.9.136-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>dev.samstevens.totp</groupId>
            <artifactId>totp</artifactId>
            <version>1.7</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.6</version>
        </dependency>

        <dependency>
            <groupId>br.tur.reservafacil.soa</groupId>
            <artifactId>gw-security</artifactId>
            <version>0.0.5</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.beryx/streamplify -->
        <dependency>
            <groupId>org.beryx</groupId>
            <artifactId>streamplify</artifactId>
            <version>1.1.1</version>
        </dependency>

        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>1.6.13</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-elasticsearch</artifactId>
            <version>3.1.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.11.1001</version>
        </dependency>

        <!-- OCI -->
        <dependency>
            <groupId>com.oracle.oci.sdk</groupId>
            <artifactId>oci-java-sdk-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oracle.oci.sdk</groupId>
            <artifactId>oci-java-sdk-addons-sasl-oke-workload-identity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oracle.oci.sdk</groupId>
            <artifactId>oci-java-sdk-addons-oke-workload-identity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oracle.oci.sdk</groupId>
            <artifactId>oci-java-sdk-common-httpclient-jersey</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.glassfish.jersey.connectors</groupId>
                    <artifactId>jersey-apache-connector</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.oracle.oci.sdk</groupId>
            <artifactId>oci-java-sdk-objectstorage</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oracle.oci.sdk</groupId>
            <artifactId>oci-java-sdk-addons-resteasy-client-configurator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.14</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <!-- Import dependency management from Spring Boot -->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.oracle.oci.sdk</groupId>
                <artifactId>oci-java-sdk-bom</artifactId>
                <version>3.53.1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.oracle.oci.sdk</groupId>
                <artifactId>oci-java-sdk-common-httpclient-jersey</artifactId>
                <version>3.53.1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>prod</id>
            <properties>
                <build.script>dev-build</build.script>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <configuration>
                            <excludeDevtools>true</excludeDevtools>
                            <profiles>
                                <profile>prod</profile>
                            </profiles>
                        </configuration>
                    </plugin>

                    <plugin>
                        <artifactId>maven-war-plugin</artifactId>
                        <version>2.6</version>
                        <configuration>
                            <warName>${artifactIdForWarFile}##${git.commitsCount}-${project.version}</warName>
                            <failOnMissingWebXml>false</failOnMissingWebXml>
                        </configuration>
                    </plugin>

                </plugins>
            </build>
            <dependencies>
                <dependency>
                    <groupId>org.postgresql</groupId>
                    <artifactId>postgresql</artifactId>
                    <version>${postgresql.version}</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>cvc-hom</id>
            <properties>
                <build.script>dev-build</build.script>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <configuration>
                            <excludeDevtools>true</excludeDevtools>
                            <profiles>
                                <profile>cvc-hom</profile>
                            </profiles>
                        </configuration>
                    </plugin>

                    <plugin>
                        <artifactId>maven-war-plugin</artifactId>
                        <version>2.6</version>
                        <configuration>
                            <warName>${artifactIdForWarFile}##${git.commitsCount}-${project.version}</warName>
                            <failOnMissingWebXml>false</failOnMissingWebXml>
                        </configuration>
                    </plugin>

                </plugins>
            </build>
            <dependencies>
                <dependency>
                    <groupId>org.postgresql</groupId>
                    <artifactId>postgresql</artifactId>
                    <version>${postgresql.version}</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>oci-cvc-hom</id>
            <properties>
                <build.script>dev-build</build.script>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <configuration>
                            <excludeDevtools>true</excludeDevtools>
                            <profiles>
                                <profile>oci-cvc-hom</profile>
                            </profiles>
                        </configuration>
                    </plugin>

                    <plugin>
                        <artifactId>maven-war-plugin</artifactId>
                        <version>2.6</version>
                        <configuration>
                            <warName>${artifactIdForWarFile}##${git.commitsCount}-${project.version}</warName>
                            <failOnMissingWebXml>false</failOnMissingWebXml>
                        </configuration>
                    </plugin>

                </plugins>
            </build>
            <dependencies>
                <dependency>
                    <groupId>org.postgresql</groupId>
                    <artifactId>postgresql</artifactId>
                    <version>${postgresql.version}</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>hom</id>
            <properties>
                <build.script>prod-build</build.script>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <configuration>
                            <profiles>
                                <profile>hom</profile>
                            </profiles>
                        </configuration>
                    </plugin>

                    <plugin>
                        <artifactId>maven-war-plugin</artifactId>
                        <version>2.6</version>
                        <configuration>
                            <warName>${artifactIdForWarFile}##${git.commitsCount}-${project.version}</warName>
                            <failOnMissingWebXml>false</failOnMissingWebXml>
                            <!--<warName>${artifactIdForWarFile}</warName>-->
                        </configuration>
                    </plugin>

                </plugins>
            </build>
            <dependencies>
                <dependency>
                    <groupId>org.postgresql</groupId>
                    <artifactId>postgresql</artifactId>
                    <version>${postgresql.version}</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>dev</id>
            <properties>
                <build.script>dev-build</build.script>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <finalName>${artifactIdForWarFile}</finalName>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <configuration>
                            <profiles>
                                <profile>dev</profile>
                            </profiles>
                            <!--
                            mvn clean spring-boot:run -Drun.jvmArguments="-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=y,address=5005"
                            -->
                        </configuration>
                    </plugin>
                </plugins>
            </build>
            <dependencies>
                <dependency>
                    <groupId>org.postgresql</groupId>
                    <artifactId>postgresql</artifactId>
                    <version>${postgresql.version}</version>
                </dependency>
            </dependencies>
        </profile>

    </profiles>

</project>
