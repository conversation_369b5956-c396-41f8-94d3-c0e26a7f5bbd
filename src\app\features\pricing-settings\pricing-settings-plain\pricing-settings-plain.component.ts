import { ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ActivatedRoute, Router } from '@angular/router';
import { nanoid } from 'nanoid';
import { NgxObjectDiffService } from 'ngx-object-diff';
import { Restangular } from 'ngx-restangular';
import * as XLSX from 'xlsx';
import { environment } from '../../../../environments/environment';
import { AuthService } from '../../../core/services/auth.service';
import { RedirectService } from '../../../core/services/redirect.service';

const TIPO_ACAO_ERRO: string = "ERRO";

@Component({
  selector: 'app-pricing-settings-plain',
  templateUrl: './pricing-settings-plain.component.html',
  styleUrls: ['./pricing-settings-plain.component.scss'],
})
export class PricingSettingsPlainComponent implements OnInit {
  public loading: boolean = true;
  public title: string;
  public linkedCommercialPricingSettings: any[];
  loadingSearch = false;
  protected readonly RESOURCE_NAME: string = 'configuracoesPrecificacao';

  private initialized: boolean = false;
  pricingSettingsPlainGroup: FormGroup;
  private idUpload: string = "";

  data = {
    empresas: null,
    tipoAcao: null
  };
  isEmpresaSelected: boolean = false;
  dataList = [];
  processData: any[] = [];
  analyzedData: any[] = [];
  empresaSelecionada: any;
  isAnalisy: boolean = false;
  errorCount: number = 0;
  @ViewChild('uploadFile') uploadFile: ElementRef;
  constructor(
    private _formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef,
    private router: Router,
    private restangular: Restangular,
    private snackBar: MatSnackBar,
    private authService: AuthService,
    private redirectService: RedirectService,
    private dialog: MatDialog,
    private objectDiff: NgxObjectDiffService
  ) { }

  public async ngOnInit() {
    this.loadingSearch = false;
    this.pricingSettingsPlainGroup = this._formBuilder.group({
      empresas: [this.data ? this.data.empresas : null],
      tipoAcao: [this.data ? this.data.tipoAcao : null]
    });

    this.loading = false;
    this.initialized = false;
  }

  public onSubmit() {
    try {
    } catch (error) {
    } finally {
      this.loading = false;
    }
  }

  onEmpresaSelected(event: any) {
    const selectedValue = event;
    if (
      this.pricingSettingsPlainGroup.get('empresas').value !== selectedValue
    ) {
      this.pricingSettingsPlainGroup.get('empresas').setValue(selectedValue);
      this.empresaSelecionada = selectedValue;
      this.isEmpresaSelected = true;
      this.processData = [];
      if (this.uploadFile)
        this.uploadFile.nativeElement.value = '';
      this.changeDetectorRef.detectChanges();
    }
  }

  onTipoAcaoSelected(event: any) {
    const selectedValue = event;
    if (this.pricingSettingsPlainGroup.get('tipoAcao').value !== selectedValue) {
      this.pricingSettingsPlainGroup.get('tipoAcao').setValue(selectedValue);
      this.changeDetectorRef.detectChanges();
    }
  }

  excelSerialToDate(serial: number): string {
    const excelStartDate = new Date(1899, 11, 30);
    return new Date(excelStartDate.getTime() + serial * 86400000)
      .toISOString()
      .split('T')[0];
  }

  handleFileInput(files: FileList) {
    const fileToUpload = files.item(0);
    if (fileToUpload) {
      console.log('Processamento de arquivo iniciado.');
      const reader = new FileReader();
      reader.onload = async (e: any) => {
        try {
          const arrayBuffer = e.target.result;
          const wb = XLSX.read(arrayBuffer, { type: 'array' });
          const wsname = wb.SheetNames[0];
          const ws = wb.Sheets[wsname];
          const jsonData: any[] = XLSX.utils.sheet_to_json(ws, { header: 1 });

          if (jsonData.length > 0) {
            const headers = jsonData[0] as string[];
            const rows = jsonData.slice(1);

            this.processData = []; // Clear existing data
            this.isAnalisy = false; // Reset analysis flag

            rows.forEach((row) => {
              if (row.some((cell) => cell != null && cell !== '')) {
                const item = row.reduce((acc, cell, index) => {
                  if (index < headers.length) {
                    if ((headers[index] === 'DATA_EMBARQUE' ||
                      headers[index] === 'DATA_EMBARQUE_INICIO' ||
                      headers[index] === 'DATA_EMBARQUE_FIM' ||
                      headers[index] === 'DATA_EMISSAO_INICIO' ||
                      headers[index] === 'DATA_EMISSAO_FIM') && !isNaN(cell)) {
                      acc[headers[index]] = this.excelSerialToDate(cell);
                    } else {
                      acc[headers[index]] = cell;
                    }
                  }
                  return acc;
                }, {});

                this.processData.push({
                  dataEmbarque: item.DATA_EMBARQUE,
                  od: item.OD,
                  cia: item.CIA,
                  mkp: item.MKP,
                  tipoAcao: null,

                  prioridade: item.PRIORIDADE,
                  dataEmbarqueInicio: item.DATA_EMBARQUE_INICIO,
                  dataEmbarqueFim: item.DATA_EMBARQUE_FIM,
                  dataEmissaoInicio: item.DATA_EMISSAO_INICIO,
                  dataEmissaoFim: item.DATA_EMISSAO_FIM,
                  tipoVoo: item.TIPO_VOO,
                  paisOrigem: item.PAIS_ORIGEM,
                  paisDestino: item.PAIS_DESTINO,
                  aeroportoOrigem: item.AEROPORTO_ORIGEM,
                  aeroportoDestino: item.AEROPORTO_DESTINO,
                  sistemaEmissor: item.SISTEMA_EMISSOR,
                  mkpPkg: item.MKP_PKG,
                  mkpStd: item.MKP_STD,
                  taxaBolsaADT: item.TAXA_BOLSA_ADT,
                  taxaBolsaCHD: item.TAXA_BOLSA_CHD,
                  tipoConfig: item.TIPO_CONFIG ? item.TIPO_CONFIG.toUpperCase() : 'RESTRITA',
                  classe: item.CLASSE,
                });
              }
            });

            // After processing all data, send it to the backend
            await this.simulateSendData();
          }
        } catch (error) {
          console.error('Erro ao processar o arquivo: ', error);
          throw error;
        }
      };
      reader.readAsArrayBuffer(fileToUpload);
    } else {
      console.log('Nenhum arquivo selecionado.');
    }
  }

  async callServer(uri: string) {
    this.loadingSearch = true;
    try {
      if (uri == '/efetivarEmMassa' && this.empresaSelecionada.tipo == 'Regular') {
        const tipoVoo = [...new Set(this.processData.map(data => data.tipoVoo))];
        await this.restangular.all(this.RESOURCE_NAME + `/excluirPrecificacaoRegularAuto?tipoVoo=${tipoVoo.join(',')}`).remove().toPromise();
      }

      if (uri == '/efetivarEmMassa' && this.empresaSelecionada.tipo == 'Fretamento') {
        await this.restangular.all(this.RESOURCE_NAME + `/excluirPrecificacaoFretamentoAuto?empresaUpload=${this.empresaSelecionada.id}`).remove().toPromise();
      }

      const promiseSize = 5;
      const chunkSize = 1;

      const updateData = (data) => {
        this.processData.filter(d => data.some(resp => d.dataEmbarque == resp.dataEmbarque &&
          d.od == resp.od &&
          d.cia == resp.cia &&
          d.mkp == resp.mkp &&
          d.prioridade == resp.prioridade &&
          d.dataEmbarqueInicio == resp.dataEmbarqueInicio &&
          d.dataEmbarqueFim == resp.dataEmbarqueFim &&
          d.dataEmissaoInicio == resp.dataEmissaoInicio &&
          d.dataEmissaoFim == resp.dataEmissaoFim &&
          d.tipoVoo == resp.tipoVoo &&
          d.paisOrigem == resp.paisOrigem &&
          d.paisDestino == resp.paisDestino &&
          d.aeroportoOrigem == resp.aeroportoOrigem &&
          d.aeroportoDestino == resp.aeroportoDestino &&
          d.sistemaEmissor == resp.sistemaEmissor &&
          d.mkpPkg == resp.mkpPkg &&
          d.mkpStd == resp.mkpStd &&
          d.taxaBolsaADT == resp.taxaBolsaADT &&
          d.taxaBolsaCHD == resp.taxaBolsaCHD &&
          d.tipoConfig == resp.tipoConfig &&
          d.classe == resp.classe
        ))
          .forEach(d => {
            const resp = data.filter(resp => d.dataEmbarque == resp.dataEmbarque &&
              d.od == resp.od &&
              d.cia == resp.cia &&
              d.mkp == resp.mkp &&
              d.prioridade == resp.prioridade &&
              d.dataEmbarqueInicio == resp.dataEmbarqueInicio &&
              d.dataEmbarqueFim == resp.dataEmbarqueFim &&
              d.dataEmissaoInicio == resp.dataEmissaoInicio &&
              d.dataEmissaoFim == resp.dataEmissaoFim &&
              d.tipoVoo == resp.tipoVoo &&
              d.paisOrigem == resp.paisOrigem &&
              d.paisDestino == resp.paisDestino &&
              d.aeroportoOrigem == resp.aeroportoOrigem &&
              d.aeroportoDestino == resp.aeroportoDestino &&
              d.sistemaEmissor == resp.sistemaEmissor &&
              d.mkpPkg == resp.mkpPkg &&
              d.mkpStd == resp.mkpStd &&
              d.taxaBolsaADT == resp.taxaBolsaADT &&
              d.taxaBolsaCHD == resp.taxaBolsaCHD &&
              d.tipoConfig == resp.tipoConfig &&
              d.classe == resp.classe)[0];
            d.tipoAcao = resp.tipoAcao;
            d.mensagemErro = resp.mensagemErro;
            this.changeDetectorRef.detectChanges();
          });
      };

      for (let i = 0; i < this.processData.length;) {
        const promises = [];

        for (let y = 0; y < promiseSize && i < this.processData.length; y++) {
          const chunk = this.processData.slice(i, i + chunkSize);

          i += chunkSize;

          let bodyRequest = {
            dados: chunk,
            tipoUpload: this.empresaSelecionada.id,
            nomeUpload: this.empresaSelecionada.nome,
            idUpload: this.idUpload
          };

          const promise = this.restangular.all(this.RESOURCE_NAME + uri).post(bodyRequest).toPromise();
          promise.then(result => {
            updateData(result.data.dados);
          }).catch(error => {
            console.error('Erro ao tentar processar os dados.', error);
            chunk.forEach(c => {
              c.tipoAcao = "ERRO";
              c.mensagemErro = error.message;
            });
            this.changeDetectorRef.detectChanges();
          });

          promises.push(promise);
        }

        try {
          if (promises.length > 0)
            await Promise.all(promises);
        } catch (error) {
          console.error('Erro ao tentar processar os dados.', error);
        }
      }

    } finally {
      this.isAnalisy = true;
      this.loadingSearch = false;
    }
  }

  async simulateSendData() {
    await this.callServer('/analisarEmMassa');
  }

  async iniciarProcesso() {
    this.idUpload = nanoid();
    await this.callServer('/efetivarEmMassa');
  }

  filterProcessDataByTipoAcao(): any[] {
    const processDataError = this.processData.filter(i => i.tipoAcao === TIPO_ACAO_ERRO);
    this.errorCount = processDataError.length;
    if (this.pricingSettingsPlainGroup.get('tipoAcao').value?.id === TIPO_ACAO_ERRO) {
      return processDataError;
    }
    return this.processData;
  }

  public getErrorOptionName = (): string => `Erro (${this.errorCount})`

  public baixarModelo() {
    const fileName =`Modelo planilha ${this.empresaSelecionada?.tipo}.xlsx`;
    const link = document.createElement('a');
    link.href = `${environment.apiUrl}/${encodeURIComponent(fileName)}`;
    link.download = fileName;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
