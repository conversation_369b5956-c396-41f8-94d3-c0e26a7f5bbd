<div>
  <mat-progress-bar mode="query" color="warn"></mat-progress-bar>
  <div class="loader" *ngIf="loadingSearch">
    <mat-spinner></mat-spinner>
  </div>
  <form class="mn-card" [formGroup]="pricingSettingsFormGroup" fxLayoutGap="15px" (submit)="onSubmit()">
    <a class="back-button" routerLink="/pricingSettings">
      <span>Precificações</span>
    </a>
    <div fxLayout="row" fxLayoutAlign="space-between center" class="topbar custom-dark-theme">
      <div fxLayout="row" *ngIf="!isNew" fxLayoutAlign="center center">
        <h2 style="margin-bottom: 0">{{ title }}</h2>
        <mat-basic-chip class="active" *ngIf="data.statusPublicacao === 'PUBLICADA'">Publicada</mat-basic-chip>
        <mat-basic-chip class="inactive" *ngIf="data.statusPublicacao === 'NAO_PUBLICADA'">Não publicada
        </mat-basic-chip>
        <mat-basic-chip class="inactive" *ngIf="data.statusPublicacao === 'EM_HISTORICO'">Histórico</mat-basic-chip>
        <mat-basic-chip class="inactive" *ngIf="data.statusPublicacao === 'INATIVO'">Inativo</mat-basic-chip>
      </div>
      <div fxLayout="row" *ngIf="isNew">
        <h2>Nova Precificação</h2>
      </div>
      <div fxLayout="row">
        <button *ngIf="!isNew && data.historico.length" mat-stroked-button (click)="showHistory($event)">
          <mat-icon>access_time</mat-icon>
        </button>
        <a *ngIf="!isNew" mat-stroked-button href="repositories/contracts?pricingConfigurationId={{getId}}">
          <mat-icon>calendar_today</mat-icon>
        </a>
      </div>
    </div>
    <div fxLayout="column" fxLayoutGap="15px">
      <app-version-banner *ngIf="getId && !data.editavel" [canRollback]="true" [id]="getId"
      [recentId]="data.idConfigEditavel" [resourceName]="'configuracoesPrecificacao'">
      </app-version-banner>
      <mat-card>
        <mat-card-content>
          <div fxLayout="row" fxLayoutGap="15px">
            <app-input-field 
            fxFlex="{pricingSettingsFormGroup.value.tipoConfigPrecificacao === 'RESTRITA'? '75' : '100' }" name="nome" 
            label="Nome" required="true"></app-input-field>
            <app-input-field *ngIf="this.showPriority(pricingSettingsFormGroup.value.tipoConfigPrecificacao)"
            fxFlex="25" name="prioridade" label="Prioridade" min='0' max='100' type="number"
            [required]="this.showPriority(pricingSettingsFormGroup.value.tipoConfigPrecificacao)">
            </app-input-field>
            <div hidden fxLayout="row" fxLayoutGap="15px">
              <mat-checkbox formControlName="forcarEmissao" fxFlex="25" fxFlexAlign="center" *ngIf="pricingSettingsFormGroup.value.tipoConfigPrecificacao === 'PADRAO'"></mat-checkbox>
              <mat-checkbox formControlName="zerarRepasse" fxFlex="25" fxFlexAlign="center"></mat-checkbox>
            </div>
          </div>
          <div fxLayout="row" fxLayoutGap="15px">
            <app-search-box formControlName="tipoConfigPrecificacao"
              [options]="[{id:'PADRAO', nome: 'Padrão'}, {id:'RESTRITA', nome: 'Restrita'},{id:'EXCLUSAO', nome: 'Exclusão'}, {id:'SUCESSOR', nome: 'Sucessor'}]"
            displayField="nome" valueField="id" placeholder="Tipo de Configuração" fxFlex="25"
            valueAsValueField="true" [required]="true">
            </app-search-box>
            <app-search-box formControlName="tipoMarkup"
            [options]="[{id:'CIA', nome: 'Cia'}, {id:'INTELIGENTE', nome: 'Inteligente'}, {id:'LIMITE', nome: 'Limite/BPRICE'},{id:'BRUTO', nome: 'Bruto'}, {id:'LIQUIDO', nome: 'Liquido'},
            {id:'TOTAL', nome: 'Total'}]" [required]="this.isRetritaOrSucessor(pricingSettingsFormGroup.value.tipoConfigPrecificacao)"
            displayField="nome" valueField="id" placeholder="Tipo de Markup" fxFlex="25" valueAsValueField="true"
            *ngIf="this.isRetritaOrSucessor(pricingSettingsFormGroup.value.tipoConfigPrecificacao)">
            </app-search-box>
            <app-input-field type="percentage" fxFlex="25" name="comissaoFornecedor" label="Comissão Fornecedor"
            *ngIf="this.isRetrita(pricingSettingsFormGroup.value.tipoConfigPrecificacao)"
            [required]="this.isRetrita(pricingSettingsFormGroup.value.tipoConfigPrecificacao)">
            </app-input-field>
            <app-input-field type="percentage" fxFlex="25" name="incentivoFornecedor" label="Incentivo Fornecedor"
            *ngIf="this.isRetrita(pricingSettingsFormGroup.value.tipoConfigPrecificacao)"
            [required]="this.isRetrita(pricingSettingsFormGroup.value.tipoConfigPrecificacao)">
            </app-input-field>
          </div>
          <div fxLayout="row" fxLayoutGap="15px">
            <app-input-field fxFlex="33" name="tourcode" label="Tourcode"
            *ngIf="this.isRetrita(pricingSettingsFormGroup.value.tipoConfigPrecificacao)"></app-input-field>
            <app-input-field fxFlex="33" name="endosso" label="Endosso"
            *ngIf="this.isRetrita(pricingSettingsFormGroup.value.tipoConfigPrecificacao)"></app-input-field>
            <app-input-field fxFlex="33"name="condicaoPrecificacao" label="Condição Precificação"
            *ngIf="this.isRetrita(pricingSettingsFormGroup.value.tipoConfigPrecificacao)"></app-input-field>
          </div>
          <div fxLayout="row" fxLayoutGap="15px">
            <app-input-field fxFlex="100" name="osi" label="OSI"
            *ngIf="this.isRetrita(pricingSettingsFormGroup.value.tipoConfigPrecificacao)"></app-input-field>
          </div>
          <div fxLayout="row" fxLayoutGap="15px">
            <app-search-box fxFlex="100" formControlName="empresas" resource="empresas" displayField="nome"
            [searchFields]="['referencia', 'nome']" valueField="id" placeholder="Empresas" [allOption]="true" [required]="true"
            [multiple]="true">
            </app-search-box>
          </div>
          <div fxLayout="row" fxLayoutGap="1px" >
            <app-search-box fxFlex="100" formControlName="ciasAereas" resource="ciasAereas/combo" displayField="nomeTratado"
            [searchFields]="['codigo', 'nome']" valueField="codigo" placeholder="Cias. Aereas" [allOption]="true"
            [multiple]="true">
            </app-search-box>
          </div>
          <div fxLayout="row">
              <mat-checkbox formControlName="emissaoOnline" fxFlex="25" fxFlexAlign="center" *ngIf="pricingSettingsFormGroup.value.tipoConfigPrecificacao === 'PADRAO'">
              Emissão online
            </mat-checkbox>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
    <div fxLayout="column">
      <app-restriction-tree title="Restrições" [(restrictions)]="data.restricoes" style="padding: 15px 0" tipoRestricaoConfiguracao="PRECIFICACAO">
      </app-restriction-tree>
    </div>



    <app-simple-subregistry #formSubregistry  title="Markup Produto" [formDefault]="markupFormDefault"
    [(data)]="data.markups" [value2FormValue]="markupValue2Form"
    [formValue2Value]="form2MarkupValue">

      <ng-template #listTemplate let-item='item'>
        <div class="markup-container" fxLayout="column" fxLayoutAlign="space-around">
          <span>{{ item.produto.nome??'' }}</span>
          <small class="text-gray">Markup busca: {{ item.markupBusca }}
            | • Fee: {{ (item.fee * 100)}}
            | • Moeda: {{ item.moedaRepasse }}
            | • Zerar DU: {{ item.zeraDu ? "Sim" : "Não" }}
            | • Taxa bolsa/RC (Fixo): {{ item.taxaBolsa }}
            | • Taxa bolsa/RC (%): {{ (item.taxaBolsaPerc * 100)}}
          </small>
          <small class="text-gray" *ngIf="item?.codigoRcSica">• Código RC - SICA: {{item.codigoRcSica}}</small>
          <small *ngIf="pricingSettingsFormGroup.value.tipoMarkup === 'LIQUIDO'"> Markup Calculado: {{ this.getMarkupLiquidoEmPorcentagem(item.markupBusca) }}</small>
        </div>
      </ng-template>

      <ng-template #formTemplate let-getFormControl="getFormControl" let-item='item'>
        <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="space-between center">
          <app-search-box *ngIf="formSubregistry.canEditAll" [formControl]="getFormControl('produto')" resource="produtos" displayField="nome"
          valueField="nome" [searchFields]="['nome']" placeholder="Produto" fxFlex="100" required="true" [pageSize]="100">
          </app-search-box>
        </div>

        <div fxLayout="row" fxLayoutGap="15px">

          <mat-form-field appearance="standard" fxFlex="33">
            <mat-label>Markup % {{pricingSettingsFormGroup.value.tipoMarkup === 'LIQUIDO' ? '(Calculado: + ' + '%)': ''}}</mat-label>
            <input matInput (change)="decimalChange($event)" [formControl]="getFormControl('markupBusca')"
            type="number" step="0.01" placeholder="%" required>
            <span matSuffix>%</span>
          </mat-form-field>

          <mat-form-field appearance="standard" fxFlex="33">
            <mat-label>Markup Fator</mat-label>
            <input matInput (change)="factorChange($event)" [formControl]="getFormControl('markupEmFatorDivisao')" type="number"
            step="0.01" placeholder="Fator" [required]="pricingSettingsFormGroup.value.tipoMarkup !== 'LIMITE'" min="0.01"
            [readonly]='pricingSettingsFormGroup.value.tipoMarkup === "LIMITE"'>
          </mat-form-field>

          <mat-form-field appearance="standard" fxFlex="33">
            <mat-label>Markup Mínimo</mat-label>
            <input matInput [formControl]="getFormControl('markupMinimo')" type="number" step="0.01"
            placeholder="%" [required]="pricingSettingsFormGroup.value.tipoMarkup === 'LIMITE' ||  pricingSettingsFormGroup.value.tipoMarkup === 'INTELIGENTE'"
            [readonly]='pricingSettingsFormGroup.value.tipoMarkup !== "LIMITE" && pricingSettingsFormGroup.value.tipoMarkup !== "INTELIGENTE"'>
            <span matSuffix>%</span>
          </mat-form-field>
        </div>

        <div fxLayout="row" fxLayoutGap="15px">
          <app-input-field #taxaBolsa type="percentage" suffix=" " fxFlex="33" label="Taxa Bolsa/RC Fixo (ADT)"
          [control]="getFormControl('taxaBolsa')" (change)="showCodigoRCSica()" required="formSubregistry.canEditAll">
          </app-input-field>
          <app-input-field #taxaBolsaPerc type="percentage" fxFlex="33" label="Taxa Bolsa/RC % (ADT)"
          [control]="getFormControl('taxaBolsaPerc')" (change)="showCodigoRCSica()" required="formSubregistry.canEditAll">
          </app-input-field>
        </div>

        <div fxLayout="row" fxLayoutGap="15px">
          <app-input-field #taxaBolsaCHD type="percentage" suffix=" " fxFlex="33" label="Taxa Bolsa/RC Fixo (CHD)"
          [control]="getFormControl('taxaBolsaCHD')" required="formSubregistry.canEditAll">
          </app-input-field>
          <app-input-field #taxaBolsaPercCHD type="percentage" fxFlex="33" label="Taxa Bolsa/RC % (CHD)"
          [control]="getFormControl('taxaBolsaPercCHD')" required="formSubregistry.canEditAll">
          </app-input-field>
        </div>

        <div fxLayout="row" fxLayoutGap="15px">
          <app-input-field #taxaBolsaINF type="percentage" suffix=" " fxFlex="33" label="Taxa Bolsa/RC Fixo (INF)"
          [control]="getFormControl('taxaBolsaINF')" required="formSubregistry.canEditAll">
          </app-input-field>
          <app-input-field #taxaBolsaPercINF type="percentage" fxFlex="33" label="Taxa Bolsa/RC % (INF)"
          [control]="getFormControl('taxaBolsaPercINF')" required="formSubregistry.canEditAll">
          </app-input-field>
          <app-input-field
          *ngIf="showCodigoRCSica()" fxFlex="28" label="CodigoRC - SICA" [control]="getFormControl('codigoRcSica')">
          </app-input-field>
        </div>

        <div fxLayout="row" fxLayoutGap="15px">
          <app-input-field type="percentage" fxFlex="50" label="Fee %" [control]="getFormControl('fee')" required="true">
          </app-input-field>
            <app-search-box [formControl]="getFormControl('moedaRepasse')" displayField="nome" valueField="id" placeholder="Moeda"
              fxFlex="100" valueAsValueField="true" required="formSubregistry.canEditAll"
              [options]="[{id:'BRL', nome: 'Real'},
                      {id:'USD', nome: 'Dólar'},
                      {id:'ARS', nome: 'Peso Argentino'}]">
          </app-search-box>
        </div>

        <div fxLayout="row" fxLayoutGap="15px">
            <mat-checkbox [formControl]="getFormControl('zeraDu')" fxFlex="25" fxFlexAlign="center" *ngIf="this.isRetritaOrSucessor(pricingSettingsFormGroup.value.tipoConfigPrecificacao)">
            Zera Du
          </mat-checkbox>
        </div>

        <div fxLayout="row" fxLayoutGap="15px">
            <mat-checkbox [formControl]="getFormControl('zeraMkpFornecedor')" fxFlex="25" fxFlexAlign="center" *ngIf="this.isRetritaOrSucessor(pricingSettingsFormGroup.value.tipoConfigPrecificacao)">
            Zera Mkp Fornecedor
          </mat-checkbox>
        </div>

        <div fxLayout="row" fxLayoutGap="15px">
          <mat-checkbox [formControl]="getFormControl('zeraTaxaBolsaRC')" fxFlex="25" fxFlexAlign="center" *ngIf="this.isRetritaOrSucessor(pricingSettingsFormGroup.value.tipoConfigPrecificacao)">
            Zera Taxa Bolsa/RC Fornecedor
          </mat-checkbox>
        </div>
      </ng-template>
    </app-simple-subregistry>

    <mat-card *ngIf="linkedCommercialPricingSettings && linkedCommercialPricingSettings.length">
      <mat-card-title>Precificações comerciais associadas</mat-card-title>
      <mat-card-content>
        <a *ngFor="let item of this.linkedCommercialPricingSettings" (mousedown)="customEventButtonToRouter($event, item.id)">
          <div fxLayout="row" fxLayoutGap="15px" style="margin: 0.5em 0">
            <mat-card>
              <mat-card-content>
                <div fxLayout="row" fxLayoutGap="10px" fxFlex fxFlexAlign="space-between center">
                  <div fxLayout="column" fxFlex>
                    <span>{{item.nome}}</span>
                    <small fxFlex style="color: grey;">
                      <span>Prioridade: {{item.prioridade}} </span>
                    </small>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </a>
      </mat-card-content>
    </mat-card>

    <div fxLayout="row" fxLayoutAlign="end" class="topbar custom-dark-theme">
      <button *ngIf="isActive && !isNew && isActive !== undefined" color="warn" mat-flat-button (click)="removeItem($event)">Inativar</button>
      <button *ngIf="!isActive && !isNew" color="warn" mat-flat-button (click)="restoreItemDialog($event)">Restaurar</button>
      <button mat-flat-button type="button" class="btn btn-primary" color="cancel"
      routerLink="/pricingSettings">Cancelar
      </button>
      <button *ngIf="!isNew" type="button" color="accent" mat-flat-button (click)="clone()">Clonar</button>
      <button mat-flat-button type="submit" class="btn btn-primary" color="save">Salvar</button>
      <button type="button" style="background-color:slateblue" mat-flat-button
        (click)="showSaveAndPublishDialog($event)">Salvar e Publicar</button>
    </div>
  </form>
</div>
