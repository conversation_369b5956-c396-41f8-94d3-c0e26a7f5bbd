<mat-card>
  <div class="loader" *ngIf="loadingSearch">
    <mat-spinner></mat-spinner>
  </div>
  <mat-card-content class="container">
    <div>
      <form>
        <mat-form-field style="width: 100%;" appearance="outline">
          <mat-label>Carregar filtro por ID</mat-label>
          <input matInput type="number" [formControl]="filterId">
          <button matSuffix *ngIf="filterId" mat-mini-fab color="primary"
                  aria-label="Carregar filtro por ID" matTooltip="Carregar filtro por ID" (click)="loadFilterById()">
            <mat-icon [@loadingState]="iconFilterById === 'sync' ? 'loading': 'loaded' " class="loading-button">{{iconFilterById}}</mat-icon>
          </button>
        </mat-form-field>
      </form>
    </div>
    <div style="text-align: right;">
      <mat-icon *ngIf="hasLastSearchResponse" style="position: relative; right: 10px;" (click)="loadLastSearchResults()">more_horiz</mat-icon>
    </div>
    <mat-accordion multi>
      <form [formGroup]="availabilityFormGroup" (submit)="searchFlights(false)">
        <div fxLayout="column">
          <div fxLayout="row" fxLayoutGap="15px">
                        <mat-form-field fxFlex="10">
              <mat-label>ADT</mat-label>
              <input matInput formControlName="numAdt"
                     type="number" step="1" min="1" placeholder="ADT" [required]="true" oninput="value = value.replace('-', '')">
            </mat-form-field>
            <mat-form-field fxFlex="10">
              <mat-label>CHD</mat-label>
              <input matInput formControlName="numChd"
                     type="number" step="1" min="0" placeholder="CHD" oninput="value = value.replace('-', '')">
            </mat-form-field>
            <mat-form-field fxFlex="10">
              <mat-label>INF</mat-label>
              <input matInput formControlName="numInf"
                     type="number" step="1" min="0" placeholder="INF" oninput="value = value.replace('-', '')">
            </mat-form-field>
            <app-search-box #cabineField formControlName="cabine" [options]="[
                                      {id:'UNDEF', nome: ''},
                                      {id:'Economica', nome: 'Economica'},
                                      {id:'Executiva', nome: 'Executiva'},
                                      {id:'Primeira Classe', nome: 'Primeira Classe'}]" displayField="nome" valueField="id"
                            placeholder="Cabine" fxFlex="100" valueAsValueField="true" style="padding-top: 3px" [resetForm]="shouldResetSearchBoxForm">
            </app-search-box>
          </div>
          <div fxLayout="row" fxLayoutGap="15px">
            <app-search-box [required]="true" formControlName="usuario" resource="usuarios" displayField="nomeTratado"
                            valueField="nome" [searchFields]="['id', 'nome', 'sobrenome']" placeholder="Usuário" fxFlex="100" (selectionChange)="usuarioChange($event)"
                            [itemModifier]="usuarioTransform" [resetForm]="shouldResetSearchBoxForm">
            </app-search-box>
            <app-search-box [required]="true" #filialField formControlName="filial" resource="filiais" displayField="nomeTratado"
                            valueField="id" [searchFields]="['nome', 'referencia']" placeholder="Filial" fxFlex="100"
                            [itemModifier]="filialTransform" [disabled]="!availabilityFormGroup.get('usuario').value">
            </app-search-box>
          </div>
          <div fxLayout="row" fxLayoutGap="15px">
            <app-search-box [required]="this.isAgenciaRequired()" formControlName="agencia" resource="agencias"
                            displayField="nomeTratado" valueField="id" [searchFields]="['referencia','nomeFantasia']" placeholder="Agência" fxFlex="50"
                            [itemModifier]="agenciaTransform" [resetForm]="shouldResetSearchBoxForm">
            </app-search-box>
            <app-search-box formControlName="grupo" resource="grupos" displayField="nomeTratado"
                            valueField="id" [searchFields]="['nome', 'referencia']" placeholder="Grupo" fxFlex="50"
                            [itemModifier]="filialTransform" [resetForm]="shouldResetSearchBoxForm">
            </app-search-box>
          </div>
          <div class="invalid" *ngIf="this.isAgenciaInvalid()">
            Campo Agência obrigatório para empresa do tipo 'INTEGRACAO' vinculada ao usuário!<br>{{this.invalidAgenciaLabel}}
          </div>
          <div fxLayout="row">
            <app-search-box formControlName="produtos" resource="produtos" displayField="nome"
                            valueField="id" placeholder="Produtos" fxFlex="100" [resetForm]="shouldResetSearchBoxForm" multiple="true"></app-search-box>
          </div>
          <div fxLayout="row" fxLayoutGap="15px">
            <mat-form-field appearance="standard" fxFlex="50">
              <mat-label>Data Criação</mat-label>
              <input matInput [matDatepicker]="startPicker" formControlName="dataReserva" placeholder="dd/mm/yyyy">
              <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
              <mat-datepicker #startPicker></mat-datepicker>
            </mat-form-field>
            
            <app-search-box #typeSearchField formControlName="cache" [options]="[
                                      {id: true, nome: 'SIM'},
                                      {id: false, nome: 'NÃO'}]" displayField="nome" valueField="id"
                            placeholder="Usar Cache" fxFlex="100" valueAsValueField="true" style="padding-top:14px" [resetForm]="shouldResetSearchBoxForm">
            </app-search-box>

            <app-search-box #typeSearchField formControlName="tipoBusca" [options]="[
                                      {id:'GWSEARCH', nome: 'REST'},
                                      {id:'GWAEREO', nome: 'SOAP'},
                                      {id:'CONTENT_PROVIDER', nome: 'Provedor de conteúdo'}]" displayField="nome" valueField="id"
                            placeholder="Tipo de Busca" fxFlex="100" valueAsValueField="true" style="padding-top:14px" [resetForm]="shouldResetSearchBoxForm">
            </app-search-box>
          </div>
          <div>
            <app-search-box formControlName="sistEmis" resource="sistemis/json" displayField="nome"
                            valueField="nome" [searchFields]="['nome']" placeholder="Sistema Emissor" fxFlex="100" valueAsValueField="true" fxFlex="100" [resetForm]="shouldResetSearchBoxForm">
            </app-search-box>
            <app-search-box fxFlex="50" formControlName="ciasAereas" resource="ciasAereas/combo" multiple="true"
                            placeholder="Cia. Aérea" displayField="nomeTratado" valueField="codigo" [searchFields]="['codigo', 'nome']">
            </app-search-box>
          </div>
         
          
          <mat-expansion-panel>
            <mat-expansion-panel-header>
              <mat-panel-title>
                Filtros Avançados
                <mat-chip
                  *ngFor="let filter of getFilterChips()"
                  [ngStyle]="{
                    'background-color': 'red',
                    'color': 'white',
                    'margin-left': '8px',
                    'font-size': '12px',
                    'padding': '6px 8px',
                    'border-radius': '22px'}">
                    {{ filter.key }}: {{ filter.value }}
                </mat-chip>
              </mat-panel-title>
            </mat-expansion-panel-header>
            <div fxLayout="row" fxLayoutGap="15px">
              <app-input-field fxFlex="100" name="fareClass" label="Classe Tarifaria"></app-input-field>
              <app-input-field fxFlex="100" name="fareBasis" label="Base Tarifaria"></app-input-field>
              <app-input-field fxFlex="100" name="designatorCode" label="Designador Base"></app-input-field>
            </div>
            <div fxLayout="row" fxLayoutGap="15px">
              <app-input-field fxFlex="100" name="fareFamily" label="Família Tarifária" placeholder="Ex: LIGHT ou LIGHT,BASIC,FLEX"></app-input-field>
              <app-input-field fxFlex="100" name="flightNumber" label="Número do Voo" placeholder="Ex: 1234 ou 1234,5678,9012"></app-input-field>
              <app-search-box formControlName="fareType" [options]="[
                                      {id:'AMB', nome: 'AMBAS'},
                                      {id:'PR', nome: 'PRIVADA'},
                                      {id:'PU', nome: 'PUBLICA'}]" displayField="nome" valueField="id"
                              placeholder="Tipo Tarifa" fxFlex="100" valueAsValueField="true" [resetForm]="shouldResetSearchBoxForm">
              </app-search-box>
              <app-search-box formControlName="baggage" [options]="[
                                      {id:'COMSEM', nome: 'Recomendações COM e SEM bagagem'},
                                      {id:'COM', nome: 'Recomendações apenas COM bagagem'},
                                      {id:'SEM', nome: 'Recomendações apenas SEM bagagem'}]" displayField="nome" valueField="id"
                              placeholder="Bagagens" fxFlex="100" valueAsValueField="true" [resetForm]="shouldResetSearchBoxForm">
              </app-search-box>
              <app-input-field fxFlex="100" name="maxNumberOfResults" label="Numero maximo de Pesquisas"></app-input-field>
            </div>
          </mat-expansion-panel>
        </div>
        <div style="padding-top: 15px">
          <mat-card-content>
            <app-form-list title="Trechos" [formArray]="trechosFormArray" [isAvailabilitySearch]="true"
                           [newItemConfig]="trechos" maxHeight="500px" [scrollable]="true">

              <ng-template appFormListColumn="origem" title="Origem" let-form="form">
                <ng-container [formGroup]="form">
                  <app-search-box [required]="true" resource="aeroportos" formControlName="codAeroportoOrigem"  displayField="codigo" valueField="codigo"
                                  [searchFields]="['codigo','nome']"
                                  [itemModifier]="vooTransform"
                                  [resetForm]="shouldResetSearchBoxForm"
                                  placeholder="Origem" fxFlex="100"
                                  (input)="form.controls.codAeroportoOrigem.setValue($event.target.value.toUpperCase())">
                  </app-search-box>
                </ng-container>
              </ng-template>

              <ng-template appFormListColumn="destino" title="Destino" let-form="form">
                <ng-container [formGroup]="form">
                  <app-search-box [required]="true" resource="aeroportos" formControlName="codAeroportoDestino"  displayField="codigo" valueField="codigo"
                                  [searchFields]="['codigo','nome']"
                                  [itemModifier]="vooTransform"
                                  [resetForm]="shouldResetSearchBoxForm"
                                   placeholder="Destino" fxFlex="100" >
                  </app-search-box>
                </ng-container>
              </ng-template>

              <ng-template appFormListColumn="dataEmbarque" title="Data" let-form="form">
                <ng-container [formGroup]="form">
                  <app-input-field [required]="true" type="date" fxFlex="100" name="dataEmbarque" label="Data" fxflex="100"
                                   >
                  </app-input-field>
                </ng-container>
              </ng-template>
            </app-form-list>
          </mat-card-content>
        </div>
        <div class="topbar custom-dark-theme">
          <div fxLayout="row" fxLayoutAlign="space-between" style="width: 100%;">
            <button mat-flat-button type="button" color="warn" style="min-width: 170px;" (click)="resetForm()"> Resetar Definições</button>
            <div>
              <button mat-flat-button type="button" color="accent" style="min-width: 170px;" (click)="searchConfigs()">Configurações de Busca</button>
              <button mat-flat-button type="button" color="accent" style="min-width: 170px;" (click)="searchFlights(true)">Resposta CIA</button>
              <button mat-flat-button type="button" color="accent" style="min-width: 170px;" (click)="showGatewayResponse()">Resposta GATEWAY</button>
              <button mat-flat-button type="submit" class="btn btn-primary" color="save" style="min-width: 170px;">Buscar</button>
            </div>
          </div>
        </div>


      </form>
    </mat-accordion>
  </mat-card-content>
</mat-card>

