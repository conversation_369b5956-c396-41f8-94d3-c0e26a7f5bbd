<app-simple-form
  listUrl="/configuracaoProduto"
  listName="Bloqueio venda de Aéreo sem Hotel"
  [title]="title"
  titleNew="Nova configuração do produto"
  apiUrl="configuracaoProduto"
  [formDefault]="formDefault"
  [value2FormValue]="value2FormValue"
>
  <ng-template
    #formTemplate
    let-getFormControl="getFormControl"
    let-form="form"
  >
    <mat-card [formGroup]="form">
      <mat-card-content>
        <div fxLayout="row" fxLayoutGap="15px">
          <app-search-box
            fxFlex="100"
            [formControl]="getFormControl('nome')"
            resource="produtos"
            displayField="nome"
            valueField="nome"
            [searchFields]="['nome']"
            placeholder="Produto"
            valueAsValueField="true"
            required="true"
          >
          </app-search-box>
        </div>
        <div fxLayout="row" fxLayoutGap="15px">
          <app-input-field
            fxFlex="100"
            label="Descrição"
            name="descricao"
          >
          </app-input-field>
        </div>
        <div fxLayout="row" fxLayoutGap="15px">
          <app-search-box
            fxFlex="33"
            [formControl]="getFormControl('ciasAereas')"
            resource="ciasAereas/combo"
            displayField="nomeTratado"
            valueField="codigo"
            placeholder="Cias Aéreas"
            [searchFields]="['codigo', 'nome']"
            multiple="true"
          >
          </app-search-box>
          <app-search-box
            fxFlex="34"
            [formControl]="getFormControl('filiais')"
            resource="filiais"
            displayField="nome"
            valueField="id"
            [searchFields]="['referencia', 'nome']"
            placeholder="Filiais"
            multiple="true"
          >
          </app-search-box>
          <app-search-box
            fxFlex="33"
            [formControl]="getFormControl('empresas')"
            resource="empresas"
            displayField="nomeTratado"
            valueField="id"
            placeholder="Empresas"
            [searchFields]="['referencia', 'nome']"
            multiple="true"
          >
          </app-search-box>
        </div>
      </mat-card-content>
    </mat-card>
  </ng-template>
</app-simple-form>
