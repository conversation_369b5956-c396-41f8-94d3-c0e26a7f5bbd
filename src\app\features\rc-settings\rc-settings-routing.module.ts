import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import crudRoutes from 'src/app/core/utils/crud-routes';
import {RcSettingsFormComponent} from "./rc-settings-form/rc-settings-form.component";
import {RcSettingsListComponent} from "./rc-settings-list/rc-settings-list.component";


const routes: Routes = [
  { path: '', component: RcSettingsListComponent, pathMatch: 'full' },
  { path: 'new', component: RcSettingsFormComponent, pathMatch: 'full' },
  { path: ':id', component: RcSettingsFormComponent, pathMatch: 'full' },
  { path: ':cloneId', component: RcSettingsFormComponent, pathMatch: 'full'}]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RcSettingsRoutingModule {
}
