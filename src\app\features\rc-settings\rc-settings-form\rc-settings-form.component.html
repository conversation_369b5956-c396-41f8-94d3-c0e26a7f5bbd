<div>
  <div *ngIf="loading">
    <mat-progress-bar mode="query" color="warn"></mat-progress-bar>
  </div>
  <form class="mn-card" [formGroup]="rcSettingsFormGroup" fxLayoutGap="15px" (submit)="onSubmit()">
    <a class="back-button" routerLink="/configuracaoRc">
      <span>Configuração RC</span>
    </a>
    <div fxLayout="row" fxLayoutAlign="space-between center" class="topbar custom-dark-theme">
      <div fxLayout="row" *ngIf="!isNew" fxLayoutGap="5px">
        <h2>{{ title }}</h2>
        <mat-basic-chip class="active" *ngIf="data.statusPublicacao === 'PUBLICADA'">Publicada</mat-basic-chip>
        <mat-basic-chip class="inactive" *ngIf="data.statusPublicacao === 'NAO_PUBLICADA'">Não publicada</mat-basic-chip>
        <mat-basic-chip class="inactive" *ngIf="data.statusPublicacao === 'EM_HISTORICO'">Histórico</mat-basic-chip>
        <mat-basic-chip class="inactive" *ngIf="data.statusPublicacao === 'INATIVO'">Inativo</mat-basic-chip>
      </div>
      <div fxLayout="row" *ngIf="isNew">
        <h2>Nova Configuração RC</h2>
      </div>
      <button *ngIf="!isNew && data.historico.length" mat-stroked-button (click)="showHistory($event)">
        <mat-icon>access_time</mat-icon>
      </button>
    </div>
    <div fxLayout="column" fxLayoutGap="15px">
      <app-version-banner *ngIf="getId && !data.editavel" [id]="getId" [recentId]="data.idConfigEditavel">
      </app-version-banner>
      <mat-card>
        <mat-card-content>
          <div fxLayout="row" fxLayoutGap="15px">
            <app-input-field fxFlex="80" name="nome" label="Nome" required="true" maxlength="50"></app-input-field>
            <app-input-field type="number" fxFlex="20" name="prioridade" label="Prioridade" required="true" min="0" max="100">
            </app-input-field>
          </div>
          <div fxLayout="row" fxLayoutGap="15px">
            <app-search-box fxFlex="100" formControlName="empresas" resource="empresas" displayField="nome" [multiple]="true" [searchFields]="['referencia', 'nome']" valueField="id" placeholder="Empresas" required="true">
            </app-search-box>
          </div>
          <div fxLayout="row" fxLayoutGap="15px">
            <app-search-box fxFlex="100" formControlName="ciasAereas" resource="ciasAereas/combo" displayField="nomeTratado" valueField="id" placeholder="Cias. Aéreas" [searchFields]="['codigo', 'nome']" [multiple]="true">
            </app-search-box>
          </div>
          <div fxLayout="row" fxLayoutGap="15px">
            <app-search-box formControlName="agencias" resource="agencias" displayField="nomeFantasia" [multiple]="true" valueField="id" [searchFields]="['nomeFantasia', 'referencia']" placeholder="Agências" fxFlex="100" required="true">
            </app-search-box>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
    <div fxLayout="column">
      <app-restriction-tree [copyRestrictions]="true" title="Restrições" [(restrictions)]="data.restricoes" style="padding: 15px 0" tipoRestricaoConfiguracao="CONFIGURACAO_RC">
      </app-restriction-tree>
    </div>

    <div fxLayout="column" style="padding: 15px 0">
      <ng-container [formGroup]="rcSettingsFormGroup">
        <app-simple-subregistry #formSubregistry title="Produtos" [formDefault]="{produto: [], codigoRcSica: '', taxaBolsa: 0, taxaBolsaPerc: 0, taxaBolsaCHD: 0, taxaBolsaPercCHD: 0, taxaBolsaINF: 0, taxaBolsaPercINF: 0, moedaRepasse: ''}" [(data)]="data.produtos" [value2FormValue]="productValue2Form()" [formValue2Value]="form2ProductValue()">
          <ng-template #listTemplate let-item='item'>
            <div fxLayout="column">
              <span>{{item?.produto?.nome}} - {{item?.codigoRcSica}}</span>
              <small style="color: grey">• Código RC: {{ item?.codigoRcSica }}
                | • Taxa bolsa (Fixo): {{ item.taxaBolsa }}
                | • Taxa bolsa (%): {{ (item.taxaBolsaPerc * 100)}}
                | • Taxa bolsa Chd (Fixo): {{ item.taxaBolsaCHD }}
                | • Taxa bolsa Chd (%): {{ (item.taxaBolsaPercCHD * 100)}}
                | • Taxa bolsa Inf (Fixo): {{ item.taxaBolsaINF }}
                | • Taxa bolsa inf (%): {{ (item.taxaBolsaPercINF * 100)}}
                | • Moeda: {{ item.moedaRepasse }}
              </small>
            </div>
          </ng-template>
          <ng-template #formTemplate let-getFormControl="getFormControl">
            <div fxLayout="column" fxLayoutGap="15px">

              <div fxLayout="column" fxLayoutGap="15px">
                <app-search-box fxFlex="100" [formControl]="getFormControl('produto')" resource="produtos" displayField="nome" [searchFields]="['nome']" valueField="nome" placeholder="Produto" [pageSize]="100" *ngIf="formSubregistry.canEditAll">
                </app-search-box>
              </div>

              <div fxLayout="row" fxLayoutGap="15px">
                <app-input-field type="percentage" suffix=" " fxFlex="33" label="Taxa Bolsa/RC Fixo (ADT)" [control]="getFormControl('taxaBolsa')" required="true">
                </app-input-field>
                <app-input-field type="percentage" fxFlex="33" label="Taxa Bolsa/RC % (ADT)" [control]="getFormControl('taxaBolsaPerc')" required="true">
                </app-input-field>
              </div>

              <div fxLayout="row" fxLayoutGap="15px">
                <app-input-field type="percentage" suffix=" " fxFlex="33" label="Taxa Bolsa/RC Fixo (CHD)" [control]="getFormControl('taxaBolsaCHD')" required="true">
                </app-input-field>
                <app-input-field type="percentage" fxFlex="33" label="Taxa Bolsa/RC % (CHD)" [control]="getFormControl('taxaBolsaPercCHD')" required="true">
                </app-input-field>
              </div>

              <div fxLayout="row" fxLayoutGap="15px">
                <app-input-field type="percentage" suffix=" " fxFlex="33" label="Taxa Bolsa/RC Fixo (INF)" [control]="getFormControl('taxaBolsaINF')" required="true">
                </app-input-field>
                <app-input-field type="percentage" fxFlex="33" label="Taxa Bolsa/RC % (INF)" [control]="getFormControl('taxaBolsaPercINF')" required="true">
                </app-input-field>
                <app-input-field fxFlex="28" label="codigoRcSica" [control]="getFormControl('codigoRcSica')" required="true">
                </app-input-field>
              </div>

              <div fxLayout="row" fxLayoutGap="15px">
                <app-search-box [formControl]="getFormControl('moedaRepasse')" displayField="nome" valueField="id" placeholder="Moeda" fxFlex="100" valueAsValueField="true" required="true" [options]="[{id:'BRL', nome: 'Real'},
                      {id:'USD', nome: 'Dólar'},
                      {id:'ARS', nome: 'Peso Argentino'}]">
                </app-search-box>
              </div>

            </div>
          </ng-template>
        </app-simple-subregistry>
      </ng-container>
    </div>

    <div fxLayout="row" fxLayoutAlign="end" class="topbar custom-dark-theme">
      <button *ngIf="isActive && !isNew && isActive !== undefined" color="warn" mat-flat-button (click)="removeItem($event)">Inativar</button>
      <button *ngIf="!isActive && !isNew" color="warn" mat-flat-button (click)="restoreItemDialog($event)">Restaurar</button>
      <button *ngIf="!isNew" type="button" color="accent" mat-flat-button (click)="clone()">Clonar</button>
      <button mat-flat-button type="button" class="btn btn-primary" color="cancel" routerLink="/configuracaoRc">Cancelar
      </button>
      <button mat-flat-button type="submit" class="btn btn-primary" color="save">Salvar</button>
      <button type="button" style="background-color:slateblue" mat-flat-button (click)="showSaveAndPublishDialog($event)">Salvar e Publicar</button>
    </div>
  </form>
</div>