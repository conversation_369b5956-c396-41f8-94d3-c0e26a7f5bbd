import {Component, OnInit} from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON>, FormGroup, Validators} from "@angular/forms";
import {ActivatedRoute, Router} from "@angular/router";
import {Restangular} from "ngx-restangular";
import {MatSnackBar} from '@angular/material/snack-bar';
import {SnackBarComponent} from "../../../shared/components/snack-bar/snack-bar.component";
import {DialogData} from "../../../shared/components/dialog/DialogData";
import {MatDialog} from '@angular/material/dialog';
import {DialogComponent} from "../../../shared/components/dialog/dialog.component";
import {NgxObjectDiffService} from 'ngx-object-diff';
import {SimpleHistoryComponent} from 'src/app/shared/components/simple-history/simple-history.component';

@Component({
  selector: 'app-commercial-pricing-settings-form',
  templateUrl: './commercial-pricing-settings-form.component.html',
  styleUrls: ['./commercial-pricing-settings-form.component.scss']
})
export class CommercialPricingSettingsFormComponent implements OnInit {
  public get isNew(): boolean {
    return !(this.currentId !== null && typeof this.currentId !== 'undefined');
  }

  public get getId(): number {
    return this.currentId;
  }

  public get isActive(): boolean {
    return this.active;
  }

  public get cloneId() {
    let cloneId = undefined;
    this.route.queryParams.subscribe((params) => {
      cloneId = params.cloneId
    })
    return cloneId;
  }

  public loading: boolean = true;
  public active: boolean = undefined;
  public title: string;

  protected readonly RESOURCE_NAME: string = "configuracoesPrecificacaoComercial";

  private currentId: number = undefined;
  private initialized: boolean = false;
  commercialPricingSettingsFormGroup: FormGroup;


  data = {
    versao: null,
    nome: null,
    prioridade: null,
    empresas: [],
    ciasAereas: [],
    agencias: [],
    restricoes: [],
    historico: [],
    ativo: null,
    editavel: null,
    statusPublicacao: null,
    status: null,
    idOrigem: null,
    idConfigEditavel: null,
    tipoConfigPrecificacao: null,
    produtos: [],

    rentabilidadeMax: [],

    codigoPrecificacaoComercial: null,
  };

  constructor(
    private _formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private restangular: Restangular,
    private snackBar: MatSnackBar,
    private objectDiff: NgxObjectDiffService,
    private dialog: MatDialog) {

  }

  public async ngOnInit() {
    this.commercialPricingSettingsFormGroup = this._formBuilder.group({
      nome: [this.data ? this.data.nome : null, [Validators.required]],
      prioridade: [this.data ? this.data.prioridade : null, [Validators.required]],
      empresas: [this.data ? this.data.empresas : null],
      ciasAereas: [this.data ? this.data.ciasAereas : null],
      agencias: [this.data ? this.data.agencias : null],
      restricoes: [this.data ? this.data.restricoes : null],
      historico: [this.data.historico ? this.data.historico : []],
      editavel: [this.data ? this.data.editavel : null],
      ativo: [this.data ? this.data.ativo : null],
      idConfigEditavel: [this.data ? this.data.idConfigEditavel : null],
      tipoConfigPrecificacao: [this.data ? this.data.tipoConfigPrecificacao : null],
      produtos: [this.data ? this.data.produtos : null],
      statusPublicacao: [this.data ? this.data.statusPublicacao : null],
      versao: [this.data ? this.data.versao : null],
      idOrigem: [this.data ? this.data.idOrigem : null],
      codigoPrecificacaoComercial: [this.data ? this.data.codigoPrecificacaoComercial : null],
    });

    this.currentId = this.route.snapshot.params['id'];

    this.loading = false;
    this.initialized = true;

    if (!isNaN(this.currentId)) {
      this.loading = true;
      try {
        await this.loadItem(this.currentId);
      } finally {
        this.loading = false;
      }
    } else if (!isNaN(this.cloneId)) {
      this.loading = true;
      try {
        await this.loadClone(this.cloneId);
      } finally {
        this.loading = false;
      }
    }
  }

  protected async loadItem(id: number): Promise<void> {
    const response = await this.findById(id);
    await this.setValueItem(response)
  }

  protected async loadClone(id: any): Promise<void> {
    const response = await this.findCloneById(id);

    await this.setValueItem(response);
  }

  protected async setValueItem(data: any): Promise<void> {

    const {
      nome,
      prioridade,
      empresas,
      ciasAereas,
      agencias,
      restricoes,
      historico,
      ativo,
      editavel,
      idConfigEditavel,
      tipoConfigPrecificacao,
      produtos,
      codigoPrecificacaoComercial,
      statusPublicacao,
      idOrigem,
      versao,
    } = data.body;


    this.data = data.body;
    this.active = ativo;
    this.title = nome;
    this.commercialPricingSettingsFormGroup.setValue({
      nome,
      prioridade,
      empresas,
      ciasAereas,
      agencias,
      restricoes,
      historico: historico ? historico : [],
      editavel,
      idConfigEditavel,
      ativo,
      tipoConfigPrecificacao,
      produtos,
      codigoPrecificacaoComercial,
      statusPublicacao,
      idOrigem,
      versao,
    });
  }

  protected async findCloneById(cloneId): Promise<void> {
    return this.restangular
      .one(this.RESOURCE_NAME, cloneId)
      .customGET('clonar').toPromise();
  }

  public async clone() {
    this.loading = true;
    try {
      this.router.navigate(['commercialPricingSettings' + '/' + 'criar'], {queryParams: {cloneId: this.currentId}});
      this.findCloneById(this.currentId).then(response => {
        this.currentId = null;
        this.setValueItem(response);
      })
    } finally {
      this.loading = false;
    }
  }

  private findById(id: number): Promise<any> {
    return this.restangular.one(this.RESOURCE_NAME, id).get().toPromise();
  }

  public onSubmit() {
    this.loading = true;
    try {
      if (this.commercialPricingSettingsFormGroup.valid
         && this.commercialPricingSettingsFormGroup.controls.ativo.value == true
        || this.isNew) {
        let request: any;
        let value = this.commercialPricingSettingsFormGroup.value;
        value.ativo = true;
        value.produtos = this.data.produtos;
        value.tipoConfigPrecificacao = "RESTRITA";
        this.insertRestrictions(value);
        if (this.currentId != null) {
          request = this.restangular.one(this.RESOURCE_NAME, this.currentId).customPUT(value);
        } else {
          request = this.restangular.all(this.RESOURCE_NAME).post(value)
        }
        request.subscribe(response => {
          this.router.navigateByUrl('/commercialPricingSettings');
        }, err => {
          this.snackBar.openFromComponent(SnackBarComponent, {
            data: {
              httpErrorResponse: err,
              buttons: [
                {label: "Mais informações", action: "information", color: "warn"},
                {label: "Fechar", action: "close", color: "warn"},
              ],
            }
          });
          this.loading = false;
        })
      } else if (this.commercialPricingSettingsFormGroup.controls.ativo.value == false ){
        this.snackBar.openFromComponent(SnackBarComponent, {
          data: {
            title: 'Alerta',
            subTitle: 'Precificação comercial Inativa, Restaure antes para poder ser editada',
            buttons: [{label: "Fechar", action: "close", color: "warn"}]
          }
        });
      } else {
        this.commercialPricingSettingsFormGroup.markAllAsTouched();
        this.snackBar.openFromComponent(SnackBarComponent, {
          data: {
            title: 'Alerta',
            subTitle: 'Verifique se todos os campos estão preenchidos corretamente',
            buttons: [{label: "Fechar", action: "close", color: "warn"}]
          }
        });
      }

    } finally {
      this.loading = false;
    }
  }


  private insertRestrictions(value: any) {
    value.restricoes = [];
    value.restricoes = this.data.restricoes
  }

  public showHistory(event: Event): void {
    event.preventDefault();
    this.data.historico.forEach((element, index) => {
      if(index === (this.data.historico.length - 1)){
        this.feedHist(element, (diff) => {
          element.diff = diff;
          const dialogRef = this.dialog.open(SimpleHistoryComponent, {
            autoFocus: false,
            maxHeight: '350px',
            width: '700px',
            data: this.data.historico,
          });
        });
      }else {
        this.feedHist(element, (diff) => {
          element.diff = diff;
        });
      }
    });
  }

  public async feedHist(hist, callback) {
    if (hist.configPrecificacaoComercial.diff === undefined) {
      await this.restangular.one(this.RESOURCE_NAME, hist.configPrecificacaoComercial.id).get()
        .subscribe(response => {
          let body = response.body;
          let historico = camposComparaveisConfiguracao(body);
          let atual = camposComparaveisConfiguracao(this.data);

          if(!historico.editavel && !historico.ativo && !atual.ativo){
            historico.ativo = true;
          }
          let diff = this.objectDiff.diff(historico, atual);
          hist.configPrecificacaoComercial.diff = diff;
          callback(this.objectDiff.toJsonDiffView(diff));
        });
    }

    function camposComparaveisConfiguracao(data) {

      return {
        nome: data.nome,
        ativo: data.ativo,
        editavel: data.editavel,
        prioridade: data.prioridade,
        condicaoPrecificacao: data.condicaoPrecificacao,
        empresas: camposComparaveisEmpresa(data.empresas),
        ciasAereas: camposComparaveisCiasAereas(data.ciasAereas),
        agencias: camposComparaveisAgencias(data.agencias),
        restricoes: camposComparaveisRestricao(data.restricoes),
        produtos: camposComparaveisProdutos(data.produtos),
      }
    }

    function camposComparaveisEmpresa(empresas) {
      return empresas.map(empresa => ({
        id: empresa.id,
        nome: empresa.nome,
      }))
    }

    function camposComparaveisCiasAereas(ciasAereas) {
      return ciasAereas.map(ciaAerea => ({
        codigo: ciaAerea.codigo,
        nome: ciaAerea.nome,
        regraTaxaDu: ciaAerea.regraTaxaDu,
        nomeTratado: ciaAerea.nomeTratado,
      }))
    }

    function camposComparaveisAgencias(agencias) {
      return agencias.map(agencia => ({
        id: agencia.id,
        nome: agencia.nomeFantasia,
      }))
    }

    function camposComparaveisProdutos(produtos) {
      return produtos.map(produtoObj => ({
        id: produtoObj.id,
        nome: produtoObj.produto.descricao,
      }))
    }

    function camposComparaveisRestricao(restricoes) {
      return restricoes.map(restricao =>
        ({
          tipoOperador: restricao.tipoOperador,
          tipoAgrupamento: restricao.tipoAgrupamento,
          tipoRestricao: restricao.tipoRestricao,
          valores: (restricao.valores)
            ? restricao.valores
              .sort((a, b) => (a.valor < b.valor) ? -1 : (a.valor > b.valor) ? 1 : 0)
              .map(valor => ({tipoRestricao: valor.tipoRestricao, valor: valor.valor}))
            : restricao.valores,
          restricoes: camposComparaveisRestricao(restricao.restricoes),
        })
      )
    }
  }
  public restoreItemDialog(event: Event) {
    event.preventDefault();
    this.showConfirmationDialog(
      {
        title: "Alerta",
        message: "Deseja restaurar o registro? ao ser restaurado o status da configuração será NÃO PUBLICADO",
        buttons: [{label: "Não", action: "close", color: "cancel"},
          {
            label: "Sim", action: (confirm) => {
              this.restoreItem()
              confirm()
            }, color: "save"
          }],
        align: "end"
      }, () => this.restoreItem())
  }

  public async restoreItem() {
    event.preventDefault();
    this.loading = true;
    try {
      await this.restangular.one(this.RESOURCE_NAME, this.currentId).customPUT({}, 'ativar').toPromise()
      await this.loadItem(this.currentId);
    } catch (err) {
      this.snackBar.openFromComponent(SnackBarComponent, {
        data: {
          httpErrorResponse: err,
          buttons: [
            {label: "Mais informações", action: "information", color: "warn"},
            {label: "Fechar", action: "close", color: "warn"}]
        }
      });
    } finally {
      this.loading = false;
    }
  }

  public removeItem(event: Event) {
    event.preventDefault();
    this.showConfirmationDialog(
      {
        title: "Alerta",
        message: "Deseja inativar o registro?",
        buttons: [{label: "Não", action: "close", color: "cancel"},
          {
            label: "Sim", action: (confirm) => {
              this.deleteItem()
              confirm()
            }, color: "save"
          }],
        align: "end"
      }, () => this.deleteItem())
  }

  private async deleteItem() {
    this.loading = true;
    try {
      await this.restangular
        .one(this.RESOURCE_NAME, this.currentId)
        .remove()
        .toPromise();
      if (this.data.statusPublicacao === 'NAO_PUBLICADA') {
        await this.router.navigateByUrl("/commercialPricingSettings");
      } else {
        await this.loadItem(this.currentId);
      }
    } finally {
      this.loading = false;
    }
  }


  private showConfirmationDialog(data: DialogData, action: Function): void {
    const dialogRef = this.dialog.open(DialogComponent, {
      maxWidth: "400px",
      data: data
    });
  }

  public productValue2Form() {
    return ({ produto, comissaoCliente,  incentivoCliente, repasseCliente}: {
      produto: any,
      comissaoCliente: number,
      incentivoCliente: number,
      repasseCliente: number,
    }) => ({
      produto: produto,
      comissaoCliente: comissaoCliente*100,
      incentivoCliente: incentivoCliente*100,
      repasseCliente: repasseCliente*100,
    });
  }

  public form2ProductValue(){
    return ({produto, comissaoCliente,  incentivoCliente, repasseCliente}: {
      produto: any,
      comissaoCliente: number,
      incentivoCliente: number,
      repasseCliente: number,
    }) => ({
      produto,
      comissaoCliente: comissaoCliente/100,
      incentivoCliente: incentivoCliente/100,
      repasseCliente: repasseCliente/100,
    });
  }

  public showSaveAndPublishDialog(event: Event) {
    event.preventDefault();
    this.showConfirmationDialog({
      title: "Salvar e Publicar Configuração",
      message: "Tem certeza de que deseja salvar e publicar este item?",
      buttons: [{label: "Cancelar", action: "close", color: "cancel"},
        {
          label: "Sim", action: (confirm) => {
            this.saveAndPublish()
            confirm()
          }, color: "save"
        }],
        align: "end"
    }, () => this.saveAndPublish());
  }

  public saveAndPublish() {
    this.loading = true;
    try {
      if (this.commercialPricingSettingsFormGroup.valid
        && this.commercialPricingSettingsFormGroup.controls.ativo.value == true
        || this.isNew) {
        let request: any;
        let value = this.commercialPricingSettingsFormGroup.value;
        value.ativo = true;
        value.produtos = this.data.produtos;
        value.tipoConfigPrecificacao = "RESTRITA";

        this.insertRestrictions(value);
        if (this.currentId != null) {
          request = this.restangular.one(this.RESOURCE_NAME, this.currentId).customPUT(value);
        } else {
          request = this.restangular.all(this.RESOURCE_NAME).post(value)
        }
        request.subscribe(async response => {
          const { id } = response.body;
          await this.restangular.one(this.RESOURCE_NAME, id).customPUT({}, 'publicar').subscribe((response) => {
            this.router.navigateByUrl('/commercialPricingSettings');
          });
        }, err => {
          this.snackBar.openFromComponent(SnackBarComponent, {
            data: {
              httpErrorResponse: err,
              buttons: [
                {label: "Mais informações", action: "information", color: "warn"},
                {label: "Fechar", action: "close", color: "warn"},
              ],
            }
          });
          this.loading = false;
        })
      } else if (this.commercialPricingSettingsFormGroup.controls.ativo.value == false ){
        this.snackBar.openFromComponent(SnackBarComponent, {
          data: {
            title: 'Alerta',
            subTitle: 'Precificação comercial Inativa,  Restaure antes para poder ser editada',
            buttons: [{label: "Fechar", action: "close", color: "warn"}]
          }
        });
      } else {
        this.commercialPricingSettingsFormGroup.markAllAsTouched();
        this.snackBar.openFromComponent(SnackBarComponent, {
          data: {
            title: 'Alerta',
            subTitle: 'Verifique se todos os campos estão preenchidos corretamente',
            buttons: [{label: "Fechar", action: "close", color: "warn"}]
          }
        });
      }

    } finally {
      this.loading = false;
    }
  }

}
