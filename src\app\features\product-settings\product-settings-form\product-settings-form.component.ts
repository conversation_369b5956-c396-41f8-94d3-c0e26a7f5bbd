import { Component } from "@angular/core";
import { Validators } from "@angular/forms";
import { MatSnackBar } from "@angular/material/snack-bar";

@Component({
  selector: "app-product-settings-form",
  templateUrl: "./product-settings-form.component.html",
})
export class ProductSettingsFormComponent {
  title: string;
  data = { nome: null, descricao: null, ciasAereas: [], filiais: [], empresas: [] };
  formDefault = {
    nome: [this.data ? this.data.nome : null, [Validators.required]],
    descricao: [this.data ? this.data.descricao : null, []],
    ciasAereas: [this.data ? this.data.ciasAereas : [], []],
    filiais: [this.data ? this.data.filiais : [], []],
    empresas: [this.data ? this.data.empresas : [], []],
  };

  constructor(private _snackBar: MatSnackBar) {}

  public value2FormValue({ nome, descricao, ciasAereas, filiais, empresas }) {
    this.title = nome;
    return { nome, descricao, ciasAereas, filiais, empresas };
  }
}
