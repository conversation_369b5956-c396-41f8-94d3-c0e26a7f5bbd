import { Clipboard } from '@angular/cdk/clipboard';
import { SPACE } from "@angular/cdk/keycodes";
import { DatePipe } from '@angular/common';
import { HttpStatusCode } from '@angular/common/http';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from "@angular/forms";
import { MatChipInputEvent } from "@angular/material/chips";
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import xmlFormat, { XMLFormatterOptions } from 'xml-formatter';
import { AvailabilitySearchService } from '../../../core/services/availability-search.service';
import { RedirectService } from '../../../core/services/redirect.service';
import { DebugDialogComponent } from '../../../shared/components/debug-dialog/debug-dialog.component';
import { DebugTariffComponent } from './bug-tariff/debug-tariff.component';
import { DetailDialogComponent } from './detail-dialog/detail-dialog.component';
import { RateTokenDialogComponent } from './rate-token-dialog/rate-token-dialog.component';

interface FlightStop {
  airport: string;
  departureDate: string;
  arrivalDate: string;
}

@Component({
  selector: 'app-availability-search-response',
  templateUrl: './availability-search-response.component.html',
  styleUrls: ['./availability-search-response.component.css']
})
export class AvailabilitySearchResponseComponent implements OnInit {

  @ViewChild('filter') filterRef: TemplateRef<any>;
  @ViewChild('paginator') paginator: MatPaginator;
  readonly separatorKeysCode: number[] = [SPACE];
  filterForm: FormGroup
  idBusca: number;
  dialogRef;
  readonly xmlMaxChars: number = 1000000

  constructor(public redirectService: RedirectService,
    private datePipe: DatePipe,
    public router: Router,
    private availabilityService: AvailabilitySearchService,
    private clipboard: Clipboard,
    private dialog: MatDialog,
    private formBuilder: FormBuilder) {

  }

  isXml: boolean;
  height = 70;
  loading = false;
  filtersApplied: any[];
  currentTab = 0;
  pageSize = 12;
  response: any;
  idGwaHashCodes: any[];
  header: any;
  tipoBusca: string;

  tabLoadTimes: Date[] = [];
  hasIdConfigBuscaOrXmls: boolean;
  showUrlChamada: boolean = false;



  ngOnInit(): void {
    this.filterForm = this.formBuilder.group({
      ciasAereas: [],
      sistEmis: [],
      fareType: [],
      flightNumber: [],
      vooDireto: [],
      vooExcluido: [],
      indexRecomendacao: null,
      segmentosSelecionados: null
    });

    this.availabilityService.xmls.subscribe(value => this.isXml = value);
    this.availabilityService.tipoBusca.subscribe(value => this.tipoBusca = value);
    this.availabilityService.response.subscribe((value) => {
      this.loading = false;
      if (value && Object.keys(value).length > 0) {
        this.filtersApplied = this.availabilityService.getRequestDetails();

        const idBuscaAnterior = this.response?.data?.idBusca;
        this.response = {};
        const pages = value.headers.get('pages');
        const itens = value.headers.get('itens');
        this.header = {
          pages,
          itens,
          currentPage: value.headers.get('Current-page'),
          totalItens: pages * itens,
        };
        this.idBusca = value?.data?.idBusca ?? idBuscaAnterior;
        if (!this.idBusca) {
          this.idBusca = value?.data?.dadosBusca?.idConfigBusca;
        }
        this.response = value;
      }
    });

    if ((this.response !== undefined) && (this.response.data !== undefined) && (this.response.data.dadosBusca !== undefined)) {
      if (this.response.status === HttpStatusCode.Ok) {
        // @ts-ignore
        this.idGwaHashCodes = Object.entries(this.response.data.dadosBusca.sistEmis);
        this.hasIdConfigBuscaOrXmls = this.availabilityService.getHasIdConfigBuscaOrXmls();
      }
    }
  }

  getFilterChips() {
    const filters = this.filtersApplied || {};
    const requiredKeys = ['baggage', 'designatorCode', 'fareBasis', 'fareClass', 'fareFamily', 'fareType', 'maxNumberOfResults', 'flightNumber'];

    const labels = {
      baggage: 'Bagagem',
      designatorCode: 'Designador Base',
      fareBasis: 'Base de Tarifa',
      fareClass: 'Classe de Tarifaria',
      fareFamily: 'Família Tarifária',
      fareType: 'Tipo de Tarifa',
      maxNumberOfResults: 'Numero Máximo de pesquisas',
      flightNumber: 'Número do Voo'
    };

    const baggageTranslations = {
      COMSEM: 'Recomendações Com e Sem bagagem',
      COM: 'Recomendações apenas com bagagem',
      SEM: 'Recomendações apenas Sem bagagem',
    };

    const fareTypeTranslations = {
      PU: 'Pública',
      PR: 'Privada',
      AMB: 'Ambas',
    };

    return requiredKeys
      .filter(key => filters[key] !== undefined && filters[key] !== null)
      .map(key => {
        let value = filters[key];
        if (key === 'baggage' && baggageTranslations[value]) {
          value = baggageTranslations[value];
        }
        if (key === 'fareType' && fareTypeTranslations[value]) {
          value = fareTypeTranslations[value];
        }
        return {
          key: labels[key] || key,
          value: value
        };
      });
  }
  public getLogoCiaUrl(codigoCia: string): string {
    if (codigoCia === 'JA') {
      codigoCia = 'WJ';
    }
    return `https://pics.avs.io/100/100/${codigoCia}.png`;
  }

  xmlFormatter(value) {
    const options: XMLFormatterOptions = {
      throwOnFailure: true
    }
    if (value) {
      try {
        return xmlFormat(value, options);
      } catch (error) {
        return value
      }
    }
  }

  downloadXML(xml: string, id: number, field: 'request' | 'response') {
    const idBusca = id ? this.idBusca : '';
    const type = field.toUpperCase();
    const fileName = `Busca - ${idBusca}-${type}`
    const blob = new Blob([xml], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();
  }

  public copyXml(xml: string): void {
    this.clipboard.copy(xml);
  }

  public copyToClipboard(text: string): void {
    this.clipboard.copy(text);
  }

  public customEventButtonToRouter(event: MouseEvent, id: string): void {
    this.redirectService.redirect('searchSettings', id, event);
  }

  public customEventButtonToRouterPrecification(event: MouseEvent, id: string): void {
    this.redirectService.redirect('pricingSettings', id, event);
  }

  getTimeLoaded(index: number) {
    if (!this.tabLoadTimes[index]) {
      this.tabLoadTimes[index] = new Date();
    }

    return this.tabLoadTimes[index];
  }

  transformDate(value) {
    // return this.datePipe.transform(value, 'yyyy-MM-dd HH:mm')
    return this.datePipe.transform(value, 'EEE dd MMMM H:mm');
  }

  loadFromCache(event) {
    this.response.data.recomendacoes = [];
    this.loading = true;
    this.currentTab = event.index;
    if (event.index === 0) {
      return this.availabilityService.cacheAvailability(['', 'TODOS'], null);
    }
    this.availabilityService.cacheAvailability(this.idGwaHashCodes[event.index - 1], null);
  }

  pagination(event) {
    this.loading = true;
    let idGwa = ['', 'TODOS'];
    if (this.currentTab !== 0) {
      idGwa = this.idGwaHashCodes[this.currentTab - 1];
    }
    this.availabilityService.cacheAvailability(idGwa, event.pageIndex + 1);
  }

  excludedPricing(correlationId, hashVoos) {
    this.availabilityService.excludedPricing(correlationId, hashVoos).then(value => {
    });
  }

  debugPaymentCondition(recomendacao) {
    this.loading = true;
    this.availabilityService.debugPaymentCondition(recomendacao).then(value => {
      this.showDebug(value.data, 'payment');
      this.loading = false;
    });
  }

  debugTariff(recomendacao) {
    this.loading = true;
    this.availabilityService.debugTariff(recomendacao).then(value => {
      this.showDebug(value.data, 'tariff');
      this.loading = false;
    });
  }

  debugComercialPrincing(recomendacao) {
    this.loading = true;
    this.availabilityService.debugComercialPrincing(recomendacao).then(value => {
      this.showDebug(value.data, 'commercialPricing');
      this.loading = false;
    });
  }

  debugFlight(recomendacao, event) {
    event.stopPropagation();
    this.loading = true;
    this.availabilityService.debugConfig(recomendacao).then(value => {
      this.loading = false;
      this.showDebugDialog(value.data);
    }).catch(err => {
      this.loading = false;
      throw err;
    });
  }

  debugPrice(recomendacao, event) {
    const lastSearchResponse = localStorage.getItem('lastSearch');
    const lastSearchResponseJson = JSON.parse(lastSearchResponse);

    const segmentos: number[] = this.filterForm.get('segmentosSelecionados')?.value?.sort((a, b) => a - b) || [];

    if (!recomendacao.segmentos || !Array.isArray(recomendacao.segmentos)) {
      throw new Error("Segmentos inválidos ou não definidos.");
    }

    let rateTokens = new Map(recomendacao.segmentos
      .filter(segmento => segmento.rateToken !== undefined)
      .map(segmento => [segmento.routeRPH, segmento.rateToken]));

    if (segmentos.length > 0) {
      const segmentosFiltrados = Array.from(segmentos)
        .map(i => recomendacao.segmentos[i])
        .filter(segmento => segmento && segmento.rateToken !== undefined);

      rateTokens = new Map(
        segmentosFiltrados.map(segmento => [segmento.routeRPH, segmento.rateToken])
      );
    }



    if (rateTokens.size === 0 || rateTokens.size > 4) {
      let msg = 'Quantidade de rate tokens excede o limite permitido! São permitidos no máximo 4 rate tokens para fazer a tarifação.'
      rateTokens.size === 0 ? msg = 'Não existe rate tokens para fazer a tarifação!' : msg;
      throw new Error(msg);
    }

    let agencyId = null
    if (lastSearchResponseJson.body.agencia !== undefined && lastSearchResponseJson.body.agencia.referencia !== undefined) {
      agencyId = lastSearchResponseJson.body.agencia.referencia
    }

    let groupId = null
    if (lastSearchResponseJson.body.grupo !== undefined && lastSearchResponseJson.body.grupo.referencia !== undefined) {
      groupId = lastSearchResponseJson.body.grupo.referencia
    }

    event.stopPropagation();
    this.loading = true;
    this.availabilityService.debugPrice(Array.from(rateTokens.values()),
      lastSearchResponseJson.body.usuario.login,
      lastSearchResponseJson.body.usuario.senha,
      lastSearchResponseJson.body.filial.referencia,
      agencyId,
      groupId)
      .then(value => {
        this.showDebugPrice(value.data.tarifacaoTestaResponse);
        this.loading = false;
      })
      .catch(error => {
        this.loading = false;
        throw error;
      });
  }

  showDebugPrice(data: any) {
    this.dialog.open(DebugTariffComponent, {
      autoFocus: false,
      maxHeight: '880px',
      width: '850px',
      data: data,
    });
  }

  showRateToken(event, rateToken) {
    event.stopPropagation();
    const data = {rateToken: rateToken}
    this.dialog.open(RateTokenDialogComponent, {
      autoFocus: false,
      data: data,
    });
  }

  showOfferId(event, offerId, decodedOfferId) {
    event.stopPropagation();
    const data = {offerId: offerId, decodedOfferId: decodedOfferId, soap: true}
    this.dialog.open(RateTokenDialogComponent, {
      autoFocus: false,
      data: data,
    });
  }

  debugFareProfileRestOrSoap(event, recomendacao) {
    event.stopPropagation();
    const flight = recomendacao?.segmentos[0]?.voos[0];
    const data = flight.fareProfileRest ? {fareProfile: flight.fareProfileRest, type: 'REST'} : {fareProfile: flight.fareProfileSoap, type: 'SOAP'}
    const dialogRef = this.dialog.open(DetailDialogComponent, {
      autoFocus: false,
      maxHeight: '460px',
      width: '1000px',
      data: {
        type:'fareProfileRestOrSoap',
        result: data
      },
    });
  }

  public backRouter(): void {
    this.router.navigate(['/availabilitySearch']);
  }



  
  public toggleUrlChamada(): void {
    this.showUrlChamada = !this.showUrlChamada;
  }

  public showDebug(data: any, type) {
    const dialogRef = this.dialog.open(DetailDialogComponent, {
      autoFocus: false,
      maxHeight: '880px',
      width: '850px',
      data: {
        type,
        result: data
      },
    });
  }

  public showDebugDialog(data: any): void {
    this.dialog.open(DebugDialogComponent, {
      autoFocus: false,
      maxHeight: '880px',
      width: '850px',
      data,
    });
  }

  calcDiffPercent(publicFare, privateFare) {
    const base = publicFare >= privateFare ? publicFare : privateFare;
    const diff = publicFare - privateFare;
    if (diff === 0) {
      return 0;
    }
    return ((diff / base) * 100).toFixed(2);
  }

  public goToPage(page: string): void {
    const pageNumber = Number(page);
    const numberOfPages = Math.ceil(this.header.totalItens / this.pageSize);

    if (!isNaN(pageNumber) && pageNumber > 0 && pageNumber <= numberOfPages) {
      const newPageIndex = pageNumber - 1;

      this.paginator.pageIndex = newPageIndex;

      const pageEvent: PageEvent = {
        pageIndex: newPageIndex,
        pageSize: this.pageSize,
        length: this.header.totalItens
      };
      this.pagination(pageEvent);
    }
  }

  icon(publicFare, privateFare) {
    const result = this.calcDiffPercent(publicFare, privateFare);
    let icon;
    switch (true) {
      case (result < 0):
        icon = 'trending_up';
        break;
      case (result > 0):
        icon = 'trending_down';
        break;
      case (result === 0):
        icon = 'trending_flat';
        break;
    }
    return icon;
  }

  iconColor(publicFare, privateFare) {
    const result = this.calcDiffPercent(publicFare, privateFare);
    let icon;
    switch (true) {
      case (result < 0):
        icon = 'red-chip';
        break;
      case (result > 0):
        icon = 'green-chip';
        break;
    }
    return icon;
  }

  openFilterDialog() {
    this.dialogRef = this.dialog.open(this.filterRef, {
      maxHeight: '880px',
      width: '850px',
    });
  }

  applyFilterDialog() {
    this.loading = true;
    this.availabilityService.filterAvailability(this.filterForm.value);
    this.loading = false;
    this.dialogRef.close();
  }


  public remove(texto) {
    const form = this.filterForm.get('flightNumber').value;
    const indexToRemove = this.filterForm.get('flightNumber').value.findIndex(object => object.valor === texto.valor);
    form.splice(indexToRemove, 1);
  }

  public addTexto(event: MatChipInputEvent): void {
    const input = event.chipInput.inputElement;
    const value = event.value;
    if ((value || '').trim()) {
      let assistTextArray = [];

      if (this.filterForm.get('flightNumber').value != null) {
        assistTextArray = this.filterForm.get('flightNumber').value;
      }
      if (!assistTextArray.includes(value)) {
        assistTextArray.push(value);
      }
      this.filterForm.get('flightNumber').setValue(assistTextArray);

    }
    if (input.value) {
      input.value = '';
    }
  }

  flightCancelled(configsPrecificacaoId) {

    if (configsPrecificacaoId === -404) {
      return true;
    }
    return false;
  }

  getStopsInfos(flights: FlightStop[]): string {

    return flights.map((flight, index) => {
      const arrivalDate = this.transformDate(flight.arrivalDate);
      const departureDate = this.transformDate(flight.departureDate);

      return `● Parada ${index + 1}: (${flight.airport}) ${arrivalDate} ➜ ${departureDate}`;
    }).join(" ");
  }

  getSearchName(tipoBusca : string) : string {
    if(tipoBusca === "GWAEREO") {
      return "(SOAP)"
    }

    if(tipoBusca === "GWSEARCH") {
      return "(REST)"
    }
  }

  toggleChecked(recomendacao: any, indexRecomendacao: number, indexSegmento: number, checked: boolean): void {
    const segmentosArray: number[] = this.filterForm.get('segmentosSelecionados')?.value || [];
    const indexRecomendacaoAtual = this.filterForm.get('indexRecomendacao')?.value;

    if (checked) {
      // Se o usuário está marcando um checkbox de uma recomendação diferente, resetamos os dados anteriores
      if (indexRecomendacaoAtual !== null && indexRecomendacaoAtual !== indexRecomendacao) {
        this.filterForm.controls['indexRecomendacao'].setValue(indexRecomendacao);
        this.filterForm.controls['segmentosSelecionados'].setValue([indexSegmento]);
        return;
      }

      // Adiciona o segmento se não existir ainda
      if (!segmentosArray.includes(indexSegmento)) {
        this.filterForm.controls['indexRecomendacao'].setValue(indexRecomendacao);

        const novoSegmento = recomendacao.segmentos[indexSegmento];
        const routeRPHNovo = novoSegmento?.routeRPH;

        // Mantém todos os segmentos que têm routeRPH diferente do novo
        const indexesFiltrados = segmentosArray.filter(i => {
          const seg = recomendacao.segmentos[i];
          return seg?.routeRPH !== routeRPHNovo;
        });

        const updatedIndexes = [...indexesFiltrados, indexSegmento];

        this.filterForm.controls['segmentosSelecionados'].setValue(updatedIndexes);
      }

    } else {
      // Remoção do segmento
      const updatedIndexes = segmentosArray.filter((i: number) => i !== indexSegmento);
      this.filterForm.controls['segmentosSelecionados'].setValue(updatedIndexes);

      // Se não restou nenhum segmento, limpamos a recomendação também
      if (updatedIndexes.length === 0) {
        this.filterForm.controls['indexRecomendacao'].setValue(null);
      }
    }

  }

  isChecked(indexRecomendacao: number, indexSegmento: number): boolean {
    const selectedRecomendacao = this.filterForm.get('indexRecomendacao')?.value;
    const segmentos: number[] = this.filterForm.get('segmentosSelecionados')?.value || [];
    return selectedRecomendacao === indexRecomendacao && segmentos.includes(indexSegmento);
  }

  trackById(index: number, item: { id: number}): number {
    return item.id;
  }

}
