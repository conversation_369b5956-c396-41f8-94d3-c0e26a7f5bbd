<div>
  <div class="loader" *ngIf="loadingSearch">
    <mat-spinner></mat-spinner>
  </div>
  <form
    class="mn-card"
    [formGroup]="pricingSettingsPlainGroup"
    fxLayoutGap="15px"
    (submit)="onSubmit()"
  >
    <a class="back-button" routerLink="/pricingSettings">
      <span>Precificações</span>
    </a>

    <!-- Seleção inicial de empresa -->
    <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="center center">
      <app-search-box
        formControlName="empresas"
        resource="precificacaoEmpresaMarkup"
        displayField="nome"
        valueField="id"
        placeholder="Selecione a Empresa"
        (selectionChange)="onEmpresaSelected($event)"
        [required]="true"
      >
      </app-search-box>
    </div>

    <div *ngIf="isEmpresaSelected">
      <div fxLayout="column" fxLayoutGap="15px">
        <mat-card>
          <mat-card-content>
          <button *ngIf="this.pricingSettingsPlainGroup.get('empresas')?.value" type="button" (click)="baixarModelo()" class="button-model">Baixar modelo</button>
            <div
              fxLayout="row"
              fxLayoutAlign="space-between center"
              class="action-row"
            >
              <div fxLayout="column" class="upload-file-container">
                <input
                  type="file"
                  (change)="handleFileInput($event.target.files)"
                  accept=".xlsx"
                  #uploadFile
                />
              </div>

              <div fxLayout="row" fxLayoutAlign="end" class="topbar custom-dark-theme" >
                <button
                  type="button"
                  style="background-color: #4caf50; color: white"
                  mat-flat-button
                  (click)="iniciarProcesso()"
                  [disabled]="loadingSearch || !isAnalisy"
                >
                  Salvar
                </button>
              </div>
            </div>
            <div class="icon-legend">
              <div fxLayout="row" fxLayoutGap="15px">
                <app-search-box
                  formControlName="tipoAcao"
                  [options]="[{id: 'ERRO', nome: this.getErrorOptionName()}]"
                  displayField="nome"
                  valueField="id"
                  placeholder="Tipo ação"
                  (selectionChange)="onTipoAcaoSelected($event)"
                >
                </app-search-box>
              </div>
              <p>
                <mat-icon class="icon-success">check_circle</mat-icon> Ação
                completada com sucesso (Criado/Atualizado)
              </p>
              <p>
                <mat-icon>add_circle_outline</mat-icon> Ação pendente de criação
              </p>
              <p><mat-icon>update</mat-icon> Ação pendente de atualização</p>
              <p><mat-icon class="icon-error">error</mat-icon> Erro ocorrido</p>
            </div>

            <div *ngIf="empresaSelecionada.tipo == 'Fretamento'">
              <table>
                <thead>
                  <tr>
                    <th>Data</th>
                    <th>Origem</th>
                    <th>Destino</th>
                    <th>CIA</th>
                    <th>MKP</th>
                    <th>Tipo Configuração</th>
                    <th>AÇÃO</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let data of this.filterProcessDataByTipoAcao()">
                    <td>{{ data.dataEmbarque }}</td>
                    <td>{{ data.od.substr(0, 3) }}</td>
                    <td>{{ data.od.substr(3, 3) }}</td>
                    <td>{{ data.cia }}</td>
                    <td>{{ data.mkp }}</td>
                    <td>{{ data.tipoConfig }}</td>
                    <td>
                      <ng-container *ngIf="data.tipoAcao; else nadaAFazerIcon">
                        <mat-icon
                          *ngIf="
                            data.tipoAcao === 'CRIADO' ||
                            data.tipoAcao === 'ATUALIZADO'
                          "
                          class="icon-success"
                        >
                          check_circle
                        </mat-icon>

                        <mat-icon
                          *ngIf="
                            data.tipoAcao === 'CRIAR' &&
                            data.tipoAcao !== 'CRIADO'
                          "
                          >add_circle_outline</mat-icon
                        >

                        <mat-icon
                          *ngIf="
                            data.tipoAcao === 'ATUALIZAR' &&
                            data.tipoAcao !== 'ATUALIZADO'
                          "
                          >update</mat-icon
                        >

                        <mat-icon
                          *ngIf="data.tipoAcao === 'ERRO'"
                          class="icon-error"
                          [title]="data.mensagemErro"
                          >error</mat-icon
                        >
                      </ng-container>
                      <ng-template #nadaAFazerIcon>
                        <mat-icon>do_not_disturb_on</mat-icon>
                      </ng-template>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div *ngIf="empresaSelecionada.tipo == 'Regular'">
              <table>
                <thead>
                  <tr>
                    <th>Prioridade</th>
                    <th>Data embarque</th>
                    <th>Data emissão</th>
                    <th>Tipo de vôo</th>
                    <th>País Origem</th>
                    <th>País Destino</th>
                    <th>Aeroporto Origem</th>
                    <th>Aeroporto Destino</th>
                    <th>CIA</th>
                    <th>Sistema Emissor</th>
                    <th>MKP PKG</th>
                    <th>MKP STD</th>
                    <th>Taxa Bolsa (ADT)</th>
                    <th>Taxa Bolsa (CHD)</th>
                    <th>Tipo Configuração</th>
                    <th>Classe</th>
                    <th>AÇÃO</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let data of this.filterProcessDataByTipoAcao()">
                    <td>{{ data.prioridade }}</td>
                    <td>{{ data.dataEmbarqueInicio }}-{{ data.dataEmbarqueFim }}</td>
                    <td>{{ data.dataEmissaoInicio }}-{{ data.dataEmissaoFim }}</td>
                    <td>{{ data.tipoVoo }}</td>
                    <td>{{ data.paisOrigem }}</td>
                    <td>{{ data.paisDestino }}</td>
                    <td>{{ data.aeroportoOrigem }}</td>
                    <td>{{ data.aeroportoDestino }}</td>
                    <td>{{ data.cia }}</td>
                    <td>{{ data.sistemaEmissor }}</td>
                    <td>{{ data.mkpPkg }}</td>
                    <td>{{ data.mkpStd }}</td>
                    <td>{{ data.taxaBolsaADT }}</td>
                    <td>{{ data.taxaBolsaCHD }}</td>
                    <td>{{ data.tipoConfig }}</td>
                    <td>{{ data.classe }}</td>
                    <td>
                      <ng-container *ngIf="data.tipoAcao; else nadaAFazerIcon">
                        <mat-icon
                          *ngIf="
                            data.tipoAcao === 'CRIADO' ||
                            data.tipoAcao === 'ATUALIZADO'
                          "
                          class="icon-success"
                        >
                          check_circle
                        </mat-icon>

                        <mat-icon
                          *ngIf="
                            data.tipoAcao === 'CRIAR' &&
                            data.tipoAcao !== 'CRIADO'
                          "
                          >add_circle_outline</mat-icon
                        >

                        <mat-icon
                          *ngIf="
                            data.tipoAcao === 'ATUALIZAR' &&
                            data.tipoAcao !== 'ATUALIZADO'
                          "
                          >update</mat-icon
                        >

                        <mat-icon
                          *ngIf="data.tipoAcao === 'ERRO'"
                          class="icon-error"
                          [title]="data.mensagemErro"
                          >error</mat-icon
                        >
                      </ng-container>
                      <ng-template #nadaAFazerIcon>
                        <mat-icon>do_not_disturb_on</mat-icon>
                      </ng-template>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </form>
</div>
