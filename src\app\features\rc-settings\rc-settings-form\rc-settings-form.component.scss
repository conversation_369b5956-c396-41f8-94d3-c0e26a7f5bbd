.company-settings-container {
  overflow-y: auto;
  scroll-snap-type: y proximity;
  padding-top: 0;
  padding-bottom: 2em;
}

.config-link {
  display: flex;
  align-items: center;
}

.mn-card {
  margin-bottom: 2em;
  padding: 1em 0.8em 1em 1.4em;
}

.mat-card {
  background: none;
  width: 100%;
}

.form-field {
  margin-top: 1px;
  width: 100%;
  min-width: 300px;
}

.supscript {
  font-size:7px;
  font-weight:initial;
  cursor: pointer;
  background-color: #495057;
  color: white;
}

.mat-icon {
  overflow: hidden;
}

.mat-column-emissor,
.mat-column-percentual {
  flex: 0 0 45%;
}

.mat-form-field {
  width: calc(100% - 15px);
}

.mat-column-acoes {
  flex: 0 0 10%;
}

.mat-row,
.mat-header-row,
.mat-footer-row {
  border-bottom-width: 0px;
}

.mat-progress-bar {
  height: 1px;
}

.mat-chip.mat-basic-chip {
  display: inline-flex;
  padding: 10px;
  border-radius: 16px;
  align-items: center;
  cursor: default;
  font-size: 12px;
  font-weight: normal;
  height: 20px;
}

.mat-chip.mat-basic-chip.active {
  background-color: black;
  color: white;
}

.mat-chip.mat-basic-chip.inactive {
  background-color: red;
  color: black;
}

.supscript-inactive {
  font-size:8px;
  font-weight:initial;
  cursor: pointer;
  background-color: red;
  color: white;
}

a {
  text-decoration: none;
  display: inline;
  cursor: pointer;
}
