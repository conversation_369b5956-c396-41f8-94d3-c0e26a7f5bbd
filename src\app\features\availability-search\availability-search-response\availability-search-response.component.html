<div>
  <div class="loader" *ngIf="loading">
    <mat-spinner></mat-spinner>
  </div>
  <ng-template *ngIf="response"  [ngTemplateOutlet]="this.hasIdConfigBuscaOrXmls ? successful : error">
  </ng-template>
</div>
<div *ngIf="!response" fxLayout="row" fxLayoutAlign="space-between center">
  <div fxLayout="column" style="margin: 5px 15px">
    <div>
      <mat-chip (click)="backRouter()" style="font-size: 20px;" >Nenhuma disponibilidade encontrada</mat-chip>
    </div>
  </div>
  <div style="padding-right: 50px; display: flex">
    <button mat-icon-button (click)="backRouter()">
      <mat-icon>
        more_horiz
      </mat-icon>
    </button>
  </div>
</div>
<ng-template #successful>
  <div fxLayout="row" fxLayoutAlign="space-between center">
    <div fxLayout="column" style="margin: 5px 15px">
      <div style="margin-bottom: 10px;">
        <span>ID Busca: </span>
        <mat-chip>{{idBusca}}</mat-chip>
      </div>
      <div style="margin-bottom: 10px;">
        <span>Filtros avançados aplicados: </span>
        <mat-chip *ngFor="let filter of getFilterChips()">
          {{ filter.key }}: {{ filter.value }}
        </mat-chip>
      </div>
      <div>
        Configuração de busca:
        <ng-container *ngIf="response?.data?.dadosBuscas?.length; else unicaConfig">
          <mat-chip *ngFor="let cfg of response.data.dadosBuscas" class="mr-2">
            <a class="config-link" (click)="customEventButtonToRouter($event, cfg.idConfigBusca)">
              <span>{{ cfg.nomeConfigBusca || cfg.idConfigBusca }}</span>
              <mat-icon [inline]="true">open_in_new</mat-icon>
            </a>
          </mat-chip>
        </ng-container>
        <ng-template #unicaConfig>
          <mat-chip>
            <a class="config-link" (click)="customEventButtonToRouter($event, response?.data?.dadosBusca?.idConfigBusca)">
              <span>{{ response?.data?.dadosBusca?.nomeConfigBusca || response?.data?.dadosBusca?.idConfigBusca }}</span>
              <mat-icon [inline]="true">open_in_new</mat-icon>
            </a>
          </mat-chip>
        </ng-template>
      </div>
      <div *ngIf="response?.data?.urlChamada" style="margin-top: 10px;">
        <span>URL da chamada: </span>
        <button mat-icon-button (click)="toggleUrlChamada()" [attr.aria-label]="showUrlChamada ? 'Ocultar URL' : 'Mostrar URL'">
          <mat-icon>{{showUrlChamada ? 'visibility_off' : 'visibility'}}</mat-icon>
        </button>
        <div *ngIf="showUrlChamada" style="margin-top: 5px; padding: 10px; background-color: #f5f5f5; border-radius: 4px; word-break: break-all;">
          <div style="display: flex; justify-content: space-between; align-items: flex-start;">
            <code style="flex: 1; font-size: 12px; line-height: 1.4;">{{response?.data?.urlChamada}}</code>
            <button mat-icon-button (click)="copyToClipboard(response?.data?.urlChamada)" matTooltip="Copiar URL" style="margin-left: 10px;">
              <mat-icon>content_copy</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div style="padding-right: 50px; display: flex">
      <button mat-icon-button (click)="backRouter()">
        <mat-icon>
          more_horiz
        </mat-icon>
      </button>
      <button mat-icon-button (click)="openFilterDialog()">
        <mat-icon>
          search
        </mat-icon>
      </button>
    </div>
  </div>
  <ng-template [ngTemplateOutlet]="isXml ? xmls : defaultList"></ng-template>
</ng-template>
<ng-template #xmls>
  <mat-tab-group *ngIf="this.hasIdConfigBuscaOrXmls">
    <mat-tab *ngFor="let xml of response.data.xmls; let i = index" [label]="'XML#' + i">
      <ng-template matTabContent>
        <div style="white-space: pre" *ngIf="xml">
          <mat-accordion>
            <mat-expansion-panel>
              <mat-expansion-panel-header>
                <mat-panel-title>
                  Request:
                </mat-panel-title>
              </mat-expansion-panel-header>
              <ng-template matExpansionPanelContent>
                <button mat-flat-button color="primary" (click)="this.copyXml(xml.request)">Copy</button>
                <pre class="xmlContainer">{{xmlFormatter(xml.request)}}</pre>
              </ng-template>
            </mat-expansion-panel>
            <mat-expansion-panel>
              <mat-expansion-panel-header>
                <mat-panel-title>
                  Response:
                </mat-panel-title>
              </mat-expansion-panel-header>
              <ng-template matExpansionPanelContent>
                <button mat-flat-button *ngIf="xml.response?.length < xmlMaxChars" color="primary" (click)="this.copyXml(xml.response)" style="top: 0; right: 0">Copy</button>
                <pre class="xmlContainer">{{xmlFormatter(xml.response?.substring(0, xmlMaxChars))}}</pre>
                <button mat-raised-button *ngIf="xml.response?.length > xmlMaxChars" (click)="downloadXML(xml.response, idBusca, 'response')">Clique aqui para fazer o download do XML completo</button>
              </ng-template>
            </mat-expansion-panel>
          </mat-accordion>
        </div>
      </ng-template>
    </mat-tab>
  </mat-tab-group>
</ng-template>
<ng-template #error>
  <div fxLayout="row" fxLayoutAlign="space-between center">
    <div fxLayout="column" style="margin: 5px 15px">
      <div>
        <mat-chip (click)="backRouter()" style="font-size: 20px;" >Nenhuma disponibilidade encontrada</mat-chip>
      </div>
    </div>
    <div style="padding-right: 50px; display: flex">
      <button mat-icon-button (click)="backRouter()">
        <mat-icon>
          more_horiz
        </mat-icon>
      </button>
    </div>
  </div>
</ng-template>


<ng-template #defaultList>
  <mat-tab-group *ngIf="response.status === 200" (selectedTabChange)="loadFromCache($event)">
    <mat-tab [label]="'TODOS#0'">
      <ng-template matTabContent>
        <ng-container [ngTemplateOutlet]="list" [ngTemplateOutletContext]="{sistEmis:'*'}"></ng-container>
      </ng-template>
    </mat-tab>
    <mat-tab *ngFor="let sistEmis of idGwaHashCodes; let i = 'index+1'" [label]="sistEmis[1] === 'UNDEF' ? 'OUTRAS' + '#CHAMADAS' + i : sistEmis[1] + '#' + i">
      <ng-template matTabContent>
        <ng-container [ngTemplateOutlet]="list" [ngTemplateOutletContext]="{sistEmis:sistEmis}"></ng-container>
      </ng-template>
    </mat-tab>
  </mat-tab-group>
</ng-template>
<ng-template #list let-sistEmis="sistEmis">
  <div *ngIf="this.response.data.recomendacoes" class="recomendacoes">
    <mat-accordion *ngFor="let recomendacao of this.response.data.recomendacoes; let indexRecomendacao = index">
      <mat-expansion-panel style="padding-top:10px">
        <mat-expansion-panel-header collapsedHeight="{{recomendacao.voos.length * height}}px" expandedHeight="{{recomendacao.voos.length * height}}px" style="height: {{recomendacao.voos.length * 60}}px; padding-left: 10px">
          <mat-panel-description style="width: 100%; color:black">
            <div fxLayout="row" fxLayoutGap="15px" style="margin: 10px; width: 100%" fxLayoutAlign="space-between center">
              <div fxLayout="column" fxFlex="grow">
                <div fxLayout="row" fxLayoutAlign="start center" *ngFor="let segments of recomendacao.segmentos; let indexSegmento = index" style="margin: 5px 0;">
                  <div fxLayout="column" style="margin-right: 20px;">
                    <mat-checkbox [checked]="isChecked(indexRecomendacao, indexSegmento)" (change)="toggleChecked(recomendacao, indexRecomendacao, indexSegmento, $event.checked)" (click)="$event.stopPropagation()" title="Tarifar">
                    </mat-checkbox>
                  </div>
                  <div fxLayout="column" fxFlex>
                    <div fxLayout="row" *ngFor="let flights of segments.voos" fxLayoutAlign="space-between center">
                      <div fxLayout="row" fxLayoutAlign="start center" style="min-width: 150px">
                        <img [src]="getLogoCiaUrl(flights.codigoCia)" class="logoCia" alt="Logo Cia">
                        <span [ngClass]="flightCancelled(recomendacao.configsPrecificacao[0].id) ? 'textGrey' : ''">
                          <ng-container *ngIf="flights.codShare">
                            {{flights.codigoCia}} * {{flights.codShare}} {{flights.numero}}
                          </ng-container>
                          <ng-container *ngIf="!flights.codShare">
                            {{flights.codigoCia}} {{flights.numero}}
                          </ng-container>
                        </span>
                      </div>
                      <div fxLayout="row" fxLayoutAlign="space-evenly center" fxFlex="45">
                        <div fxLayout="column" style="min-width: 150px;">
                          <div>
                            <span [ngClass]="flightCancelled(recomendacao.configsPrecificacao[0].id) ? 'textGrey' : '' ">
                              {{flights.aeroportoOrigem}}</span>
                          </div>
                          <div class="date">
                            <small [ngClass]="flightCancelled(recomendacao.configsPrecificacao[0].id) ? 'textGrey' : '' ">
                              {{transformDate(flights.dataOrigem)}}
                            </small>
                          </div>
                        </div>
                        <div fxLayout="column" style="min-width: 150px;">
                          <div>
                            <span [ngClass]="flightCancelled(recomendacao.configsPrecificacao[0].id) ? 'textGrey' : '' ">
                              {{flights.aeroportoDestino}}
                            </span>
                          </div>
                        <div fxLayout="row" class="date" >
                            <small [ngClass]="flightCancelled(recomendacao.configsPrecificacao[0].id) ? 'textGrey' : '' ">
                              {{transformDate(flights.dataDestino)}}
                            </small>
                          <small *ngIf="flights.flightStops?.length > 0"
                            [matTooltip]="this.getStopsInfos(flights.flightStops)"
                            matTooltipPosition="above"
                            class="custom-badge">
                              +{{ flights.flightStops.length }}
                            </small>
                          </div>
                        </div>
                      </div>
                      <div fxFlex="10">
                        <span *ngIf="flightCancelled(recomendacao.configsPrecificacao[0].id)" [ngClass]="flightCancelled(recomendacao.configsPrecificacao[0].id) ? 'textGrey' : '' ">
                          Voo Cancelado
                        </span>
                      </div>
                      <div fxLayout="row" fxLayoutGap="25px" fxFlex="15">
                        <a class="config-link" (click)="customEventButtonToRouter($event, response.data.dadosBusca.idConfigBusca)">
                          <span [ngClass]="flightCancelled(recomendacao.configsPrecificacao[0].id) ? 'textGrey' : '' " title="Classe"> {{flights.classe}}</span>
                        </a>
                        <a class="config-link" (click)="customEventButtonToRouter($event, response.data.dadosBusca.idConfigBusca)">
                          <span [ngClass]="flightCancelled(recomendacao.configsPrecificacao[0].id) ? 'textGrey' : '' " title="Base Tarifaria">
                            {{flights.baseTarifaria}}
                          </span>
                        </a>
                      </div>
                      <div fxLayout="row" fxLayoutGap="25px" fxFlex="15">
                        <span title="Família Tarifária"> {{flights.familiaTarifaria}} </span>
                      </div>
                      <div fxFlex="15" fxLayoutAlign="start end">
                        <mat-icon *ngIf="flights.bagagem === undefined || flights.bagagem >= 0" [ngClass]="flightCancelled(recomendacao.configsPrecificacao[0].id) ? 'textGrey' : '' ">{{flights.bagagem !== undefined && flights.bagagem > 0 ? 'luggage' : 'no_luggage'}}</mat-icon>
                        <span *ngIf="flights.bagagem !== undefined && flights.bagagem > 0" [ngClass]="flightCancelled(recomendacao.configsPrecificacao[0].id) ? 'textGrey' : '' ">
                          {{ flights.bagagem }} {{ flights.bagagem === 1 ? 'bagagem' : 'bagagens' }}
                        </span>
                        <span *ngIf="flights.bagagem === undefined || flights.bagagem === 0" [ngClass]="flightCancelled(recomendacao.configsPrecificacao[0].id) ? 'textGrey' : '' ">Sem bagagem</span>
                      </div>
                      <div fxLayout="row" fxFlex="15">
                        <button *ngIf="flights?.segmentRateToken" mat-icon-button (click)="showRateToken($event, flights?.segmentRateToken)" title="Rate Token">
                          <mat-icon style="font-size: 15px">key</mat-icon>
                        </button>
                        <button *ngIf="tipoBusca == 'GWAEREO'" mat-icon-button (click)="showOfferId($event, recomendacao.offerId, recomendacao.decodedOfferId)" title="Offer ID">
                          <mat-icon style="font-size: 15px">key</mat-icon>
                        </button>
                      </div>
                    </div>
                    <div style="border-top: 1px dashed #808080; width: 100%; margin-top: 5px; margin-bottom: 5px;"></div>
                  </div>
                </div>
              </div>
              <div fxLayout="row" fxFlex="15" fxLayoutGap="25px" fxLayoutAlign="space-evenly center">
                <b [ngClass]="flightCancelled(recomendacao.configsPrecificacao[0].id) ? 'textGrey' : '' ">{{recomendacao.total | currency :recomendacao.moeda}}</b>
              </div>
              <div fxLayout="row" fxFLex="15">
                <button *ngIf="tipoBusca != 'GWAEREO'" mat-icon-button (click)="debugPrice(recomendacao, $event)" title="Tarifar">
                  <mat-icon style="font-size: 15px">
                    monetization_on
                  </mat-icon>
                </button>
                <button mat-icon-button (click)="debugFlight(recomendacao, $event)" title="Depurar">
                  <mat-icon style="font-size: 15px">
                    bug_report
                  </mat-icon>
                </button>
              </div>
            </div>
          </mat-panel-description>
        </mat-expansion-panel-header>
        <div>
          Detalhes desta recomendação
          <mat-chip-list>
            <mat-chip>
              <span title="Sistema emissor - OID Busca - OID Reserva - OID Emissão">{{recomendacao.sistEmis}} - {{recomendacao.officeIdBusca}} - {{recomendacao.officeIdReserva}} - {{recomendacao.officeIdEmissao}}</span>
            </mat-chip>
            <ng-container *ngIf="recomendacao.configsPrecificacao.length > 0">
              <mat-chip-list aial-label="Configurações de precificação padrão" *ngIf="recomendacao.configsPrecificacao?.length">
                <mat-chip *ngFor="let cfg of recomendacao.configsPrecificacao; trackBy: trackById">
                  <span (click)="customEventButtonToRouterPrecification($event, cfg.id)" title="Configuração: {{ cfg.nome || 'S/Config'}}">{{ cfg.nome || 'S/Config'}}</span>
                </mat-chip>
              </mat-chip-list>
            </ng-container>
            <mat-chip *ngIf="recomendacao?.idConfigSucessor">
              <span (click)="customEventButtonToRouterPrecification($event, recomendacao.idConfigSucessor)" title="Configuração Sucessor">Config Sucessor: {{ recomendacao.idConfigSucessor }}</span>
            </mat-chip>
            <mat-chip *ngIf="recomendacao?.codigoContrato">
              <span (click)="customEventButtonToRouterPrecification($event, recomendacao.codigoContrato)" title="Codigo de contrato">{{ recomendacao.codigoContrato }}</span>
            </mat-chip>
            <mat-chip>
              <span title="Tipo de tarifa">
                {{recomendacao.tipoTarifaAcordoEnum}}
              </span>
            </mat-chip>
            <mat-chip>
              <span title="Correlation ID">
                {{recomendacao.idGwa}}
              </span>
            </mat-chip>
            <!-- <mat-chip (click)="excludedPricing(recomendacao.idGwa, recomendacao.hashVoos)">
              <span title="Recomendações excluídas">
                + Info
              </span>
            </mat-chip> -->
            <mat-chip (click)="debugPaymentCondition(recomendacao)">
              <span title="Depurar Condições de Pagamento">
                Cond.Pagamento
              </span>
            </mat-chip>

            <mat-chip (click)="debugTariff(recomendacao)">
              <span title="Depurar Perfil Tarifário Precificador">
                Perfil Tarifário Precificador
              </span>
            </mat-chip>
            <mat-chip (click)="debugFareProfileRestOrSoap($event, recomendacao)">
              <span title="Perfil Tarifário Busca">
                Perfil Tarifário Busca {{this.getSearchName(tipoBusca)}}
              </span>
            </mat-chip>
            <mat-chip (click)="debugComercialPrincing(recomendacao)">
              <span title="Depurar Precificação Comercial">
                Precificação Comercial
              </span>
            </mat-chip>
            <mat-chip *ngIf="recomendacao?.valores?.ADT?.construcaoTarifaria">
              <span title="Construcao Tarifaria ADT">
                {{recomendacao.valores.ADT.construcaoTarifaria}}
              </span>
            </mat-chip>
            <mat-chip *ngIf="recomendacao?.valores?.CHD?.construcaoTarifaria">
              <span title="Construcao Tarifaria CHD">
                {{recomendacao.valores.CHD.construcaoTarifaria}}
              </span>
            </mat-chip>
            <mat-chip *ngIf="recomendacao?.valores?.INF?.construcaoTarifaria">
              <span title="Construcao Tarifaria INF">
                {{recomendacao.valores.INF.construcaoTarifaria}}
              </span>
            </mat-chip>
          </mat-chip-list>
        </div>
        <div style="padding-left: 5px; padding-top: 5px">
          Tarifa neto: {{recomendacao.totalTarifa | currency :recomendacao.moeda}} •
          Sobretaxas (Markup, Fee, RAV) {{recomendacao.totalSobreTaxas| currency :recomendacao.moeda}} •
          Taxas (DU, Tx.Emb.){{recomendacao.totalTaxas| currency :recomendacao.moeda}} •
          Comercial (Comissão, Incentivo, Repasse) {{recomendacao.totalCliente| currency :recomendacao.moeda}}
        </div>
      </mat-expansion-panel>
    </mat-accordion>
    <div *ngIf="this.response.data.erros && header.totalItens === 0" class="recomendacoes">
      <mat-accordion *ngFor="let erro of this.response.data.erros">
        <div>{{erro.sistEmis}} - <span *ngFor="let msg of erro.mensagens">{{msg}}</span></div>
      </mat-accordion>
    </div>
    <div fxLayout="row" fxLayoutAlign="end center" class="container-paginator">
      <mat-form-field class="paginator-input-field">
        <mat-label>Ir para</mat-label>
        <input matInput type="number" #pageInput (keyup.enter)="goToPage(pageInput.value)">
      </mat-form-field>
      <mat-paginator class="mat-paginator-sticky" #paginator [pageSize]="pageSize" [pageIndex]="header.currentPage - 1" [length]="header.totalItens" (page)="pagination($event)"
      showFirstLastButtons="true">
      </mat-paginator>
    </div>
  </div>
</ng-template>


<ng-template #filter>
  <h2>Filtrar Por:</h2>
  <form [formGroup]="filterForm" (submit)="applyFilterDialog()">
    <div fxLayout="row">
      <app-search-box fxFlex="50" [formControlName]="'ciasAereas'" resource="ciasAereas/combo"
                      placeholder="Cia. Aérea" displayField="nomeTratado" valueField="codigo" [searchFields]="['codigo', 'nome']">
      </app-search-box>
      <app-search-box resource="sistemis/json" displayField="nome"
                      [formControlName]="'sistEmis'" valueField="nome" placeholder="Sistema emissor"
                      valueAsValueField="true" fxFlex="50">
      </app-search-box>
    </div>
    <div fxLayout="row">
      <app-search-box formControlName="fareType" [options]="[
                                      {id:'AMB', nome: 'AMBAS'},
                                      {id:'PR', nome: 'PRIVADA'},
                                      {id:'PU', nome: 'PUBLICA'}]" displayField="nome" valueField="id"
                      placeholder="Tipo Tarifa" fxFlex="50" valueAsValueField="true">
      </app-search-box>
      <mat-form-field style="width: 100%;">
        <mat-chip-list #chipList>
          <div *ngIf="filterForm.value != null || filterForm.value.flightNumber.length > 0">
            <mat-chip *ngFor="let text of filterForm.value.flightNumber" (removed)="remove(text)">
              {{text ? text : ''}}
              <mat-icon matChipRemove> cancel</mat-icon>
            </mat-chip>
          </div>
          <input  placeholder="Numeros Voo"
                 [matChipInputFor]="chipList"
                 [matChipInputSeparatorKeyCodes]="separatorKeysCode"
                 [matChipInputAddOnBlur]="true"
                 (matChipInputTokenEnd)="addTexto($event)"
                 style="margin-top: 3px;"
          />
        </mat-chip-list>
      </mat-form-field>
    </div>
    <div fxLayout="row">
      <app-search-box formControlName="vooDireto" [options]="[
                                      {id:'true', nome: 'Voo Direto'},
                                      {id:'false', nome: 'Todos'}]" displayField="nome" valueField="id"
                      placeholder="Segmentos" fxFlex="100" valueAsValueField="true">
      </app-search-box>
    </div>
    <div fxLayout="row">
      <app-search-box formControlName="vooExcluido" [options]="[
                                      {id:'true', nome: 'Incluir'},
                                      {id:'false', nome: 'Não Incluir'}]" displayField="nome" valueField="id"
                      placeholder="Vôos Excluídos" fxFlex="100" valueAsValueField="true">
      </app-search-box>
    </div>
    <button [mat-dialog-close]="''" mat-stroked-button aria-label="Fechar" type="button">
      <mat-icon>close</mat-icon>
    </button>
    <button mat-stroked-button color="primary" aria-label="Aplicar" type="submit">
      <mat-icon>check</mat-icon>
    </button>
  </form>
</ng-template>


