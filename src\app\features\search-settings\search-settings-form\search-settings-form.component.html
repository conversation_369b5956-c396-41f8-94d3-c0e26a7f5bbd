<div>
  <mat-progress-bar mode="query" color="warn"></mat-progress-bar>
  <div class="loader" *ngIf="loadingSearch">
    <mat-spinner></mat-spinner>
  </div>
  <form class="mn-card" [formGroup]="searchSettingsFormGroup" fxLayoutGap="15px" (submit)="onSubmit()">
    <a class="back-button" routerLink="/searchSettings">
      <span>Configuração de Busca</span>
    </a>
    <div fxLayout="row" fxLayoutAlign="space-between center" class="topbar custom-dark-theme">
      <div fxLayout="row" *ngIf="!isNew" fxLayoutGap="5px">
        <h2>{{ title }}</h2>
        <mat-basic-chip class="active" *ngIf="data.statusPublicacao === 'PUBLICADA'">Publicada</mat-basic-chip>
        <mat-basic-chip class="inactive" *ngIf="data.statusPublicacao === 'NAO_PUBLICADA'">Não publicada</mat-basic-chip>
        <mat-basic-chip class="inactive" *ngIf="data.statusPublicacao === 'EM_HISTORICO'">Histórico</mat-basic-chip>
        <mat-basic-chip class="inactive" *ngIf="data.statusPublicacao === 'INATIVO'">Inativo</mat-basic-chip>
      </div>
      <div fxLayout="row" *ngIf="isNew">
        <h2>Nova Configuração de Busca</h2>
      </div>
      <button *ngIf="!isNew && data.historico.length" mat-stroked-button (click)="showHistory($event)">
        <mat-icon>access_time</mat-icon>
      </button>
    </div>
    <div fxLayout="column" fxLayoutGap="15px">
      <app-version-banner *ngIf="getId && !data.editavel" [canRollback]="true" [id]="getId"
                          [recentId]="data.idConfigEditavel" [resourceName]="'configuracoesBusca'">
      </app-version-banner>
      <mat-card>
        <mat-card-content>
          <div fxLayout="row" fxLayoutGap="15px">
            <app-input-field fxFlex="80" name="nome" label="Nome" required="true"></app-input-field>
            <app-input-field fxFlex="20" name="prioridade" label="Prioridade" required="true"></app-input-field>
            <app-input-field *ngIf="data.peso" fxFlex="20" name="peso" label="Peso"></app-input-field>
          </div>
          <div fxLayout="row" fxLayoutGap="15px">
            <app-search-box fxFlex="50" formControlName="empresa" resource="empresas" displayField="nome"
                            [searchFields]="['referencia', 'nome']" valueField="id" placeholder="Empresa" [required]="true">
            </app-search-box>
            <app-search-box formControlName="nacInt" displayField="nome" valueField="id"
                            placeholder="Nacional/Internacional" valueAsValueField="true" fxFlex="50" [required]="true"
                            [options]="[{id:'NAC', nome: 'Nacional'},
                          {id:'INT', nome: 'Internacional'},
                          {id:'AMBOS', nome: 'Ambos'}]">
            </app-search-box>
          </div>
          <div fxLayout="row" fxLayoutGap="15px">
            <app-search-box fxFlex="100" formControlName="produtos" resource="produtos" displayField="nome"
                            [searchFields]="['nome']" valueField="id" placeholder="Produtos" [multiple]="true" [useOrToFind]="false">
            </app-search-box>
          </div>
          <div fxLayout="row" fxLayoutGap="15px">
            <app-search-box fxFlex="100" formControlName="filiais" resource="filiais" displayField="nome"
                            [searchFields]="['referencia', 'nome']" valueField="id" placeholder="Filiais" [multiple]="true">
            </app-search-box>
          </div>
          <div fxLayout="row" fxLayoutGap="15px">
            <mat-checkbox formControlName="agrupaChamadaUnica" fxFlexAlign="center">
              Chamada Única
            </mat-checkbox>
            <mat-checkbox formControlName="bestPrice" fxFlexAlign="center">
              Best Price
            </mat-checkbox>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
    <div fxLayout="column">
      <app-restriction-tree title="Restrições" [(restrictions)]="data.restricoes" style="padding: 15px 0" [tipoRestricaoConfiguracao]="'BUSCA'">
      </app-restriction-tree>
    </div>
    <div fxLayout="column" style="padding: 15px 0">
      <mat-card>
        <mat-card-title style="padding: 0 0 15px 15px;">
          <div fxLayout="row" fxLayoutAlign="space-between center" class="topbar custom-dark-theme">
            <strong>Acordo Comercial</strong>
            <button mat-button (click)="addAcordoComercial($event)">
              <mat-icon>
                add
              </mat-icon>
            </button>
          </div>
        </mat-card-title>
        <div *ngIf="!acordoComercialLoading && !isNew" style="display: flex;align-items: center;justify-content: center;" fxLayout="column">
          <mat-spinner [diameter]="30"></mat-spinner>
        </div>
        <div *ngIf="acordoComercialLoading">
          <ng-container *ngFor="let acordoForm of acordoComerciaisAgrupados.controls; let i = index">
            <mat-accordion>
              <mat-expansion-panel style="margin-bottom: 5px">
                <div *ngFor="let chamada of acordoForm.value.chamadas; let chamadaIndex = index;">
                  <sup>
                    Chamada {{chamadaIndex + 1}}
                  </sup>
                  <div *ngFor="let acordo of chamada.acordosComerciais;" fxLayout="row" fxLayoutAlign="space-between center">
                    <a target="_blank" routerLink="/tradeAgreements/{{acordo.id}}" style="color: black">
                      <div fxLayout="column" style="padding-left: 5px">
                        {{acordo.nome}}
                      </div>
                    </a>
                    <div fxLayout="row">
                      <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px">
                        <small *ngIf="acordo.dataInicioVigencia || acordo.dataFimVigencia">
                          <span *ngIf="acordo.dataInicioVigencia" style="color: red">{{acordo.dataInicioVigencia | date: 'dd/MM/yyyy HH:mm'}}</span>
                          <span *ngIf="!acordo.dataInicioVigencia" style="color: red">Sem data de inicio</span>
                          <span *ngIf="acordo.dataFimVigencia" style="color: red"> - {{acordo.dataFimVigencia | date: 'dd/MM/yyyy HH:mm'}}</span>
                          <span *ngIf="!acordo.dataFimVigencia" style="color: red">Sem data final</span>
                        </small>
                        <mat-chip-list>
                          <mat-chip *ngIf="!acordo.ativo" class="supscript-inactive" matTooltip="Acordo Inativo">Inativo</mat-chip>
                          <mat-chip *ngIf="acordo?.empresa?.id !== data?.empresa?.id" class="supscript" matTooltip="Referência Empresa">
                            {{acordo.empresa.referencia}}
                          </mat-chip>
                        </mat-chip-list>
                      </div>
                      <button mat-icon-button type="button" (click)="removeAcordoComercial($event, acordo)">
                        <mat-icon>
                          delete
                        </mat-icon>
                      </button>
                    </div>
                  </div>
                </div>
                <mat-expansion-panel-header>
                  <mat-panel-title style="min-width: 35vw">
                    {{acordoForm.value.sistEmis}}
                  </mat-panel-title>
                  <mat-panel-description>
                    {{acordoForm.value.chamadas.length}} Chamada{{acordoForm.value.chamadas.length > 1 ? 's' : ''}}
                  </mat-panel-description>
                </mat-expansion-panel-header>
              </mat-expansion-panel>
            </mat-accordion>
          </ng-container>
        </div>
      </mat-card>
    </div>

    <div fxLayout="row" fxLayoutAlign="space-between baseline" style="padding: 15px 0">
      <div fxLayout="column" fxFlex="45">
        <ng-container [formGroup]="searchSettingsFormGroup">
          <app-simple-subregistry title="Cias Aéreas Excluídas" [formDefault]="{ciaExcluida: null, sistEmis: null}" [(data)]="data.ciasExcluidas"
                                  [value2FormValue]="ciasExcluidasValue2Form()" [formValue2Value]="form2ciasExcluidasValue" (dataChange)="addExcludedCias($event)">
            <ng-template #listTemplate let-item='item'>
              <div fxLayout="row" fxLayoutAlign="space-between center">
                <div fxLayout="column">
                  <div fxLayout="row">
                    <small *ngIf="item.ciaExcluida.codigo">{{item.ciaExcluida.codigo}} -&nbsp;</small>
                    <small>{{item?.ciaExcluida?.nome}}</small>
                  </div>
                  <small style="color: grey">Sistema Emissor: {{item?.sistEmis}}</small>
                </div>
              </div>
            </ng-template>
            <ng-template #formTemplate let-getFormControl="getFormControl">
              <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="space-between center">
                <app-search-box [formControl]="getFormControl('ciaExcluida')" [multipleResources]="['ciasAereas/gruposEciasAereas?nome=', 'ciasAereas']" displayField="nome" fxFlex="50"
                                valueField="nome" [searchFields]="['codigo', 'nome']" placeholder="Cias aéreas excluída" [required]="true" (selectionChange)="verifyIfGroupOfCias($event)">
                </app-search-box>
                <app-search-box [formControl]="getFormControl('sistEmis')" resource="sistemis/json" displayField="nome" [valueAsValueField]="true"
                                valueField="nome" [searchFields]="['nome']" placeholder="Sistema emissor" fxFlex="50" [required]="true">
                </app-search-box>
              </div>
            </ng-template>
          </app-simple-subregistry>
        </ng-container>
      </div>
      <div fxLayout="column" fxFlex="45">
        <ng-container [formGroup]="searchSettingsFormGroup">
          <app-simple-subregistry title="Cias Aéreas Validadoras Excluídas" [formDefault]="{ciaExcluida: null, sistEmis: null}" [(data)]="data.ciasValidadorasExcluidas"
                                  [value2FormValue]="ciasValidadorasExcluidasValue2Form()" [formValue2Value]="form2ciasValidadorasExcluidasValue()" (dataChange)="addExcludedValidatorCias($event)">
            <ng-template #listTemplate let-item='item'>
              <div fxLayout="row" fxLayoutAlign="space-between center">
                <div fxLayout="column">
                  <div fxLayout="row">
                    <small *ngIf="item.ciaExcluida.codigo">{{item.ciaExcluida.codigo}} -&nbsp;</small>
                    <small>{{item?.ciaExcluida?.nome}}</small>
                  </div>
                  <small style="color: grey">Sistema Emissor: {{item?.sistEmis}}</small>
                </div>
              </div>
            </ng-template>
            <ng-template #formTemplate let-getFormControl="getFormControl">
              <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="space-between center">
                <app-search-box [formControl]="getFormControl('ciaExcluida')" [multipleResources]="['ciasAereas/gruposEciasAereas?nome=', 'ciasAereas']" displayField="nome" fxFlex="50"
                                valueField="nome" [searchFields]="['codigo', 'nome']" placeholder="Cias aéreas excluída" [required]="true" (selectionChange)="verifyIfGroupOfCias($event)">
                </app-search-box>
                <app-search-box [formControl]="getFormControl('sistEmis')" resource="sistemis/json" displayField="nome" [valueAsValueField]="true"
                                valueField="nome" [searchFields]="['nome']" placeholder="Sistema emissor" fxFlex="50" [required]="true">
                </app-search-box>
              </div>
            </ng-template>
          </app-simple-subregistry>
        </ng-container>
      </div>
    </div>
    <div fxLayout="row" fxLayoutAlign="end" class="topbar custom-dark-theme">
      <button *ngIf="isActive && !isNew && isActive !== undefined" color="warn" mat-flat-button (click)="removeItem($event)">Inativar</button>
      <button *ngIf="!isActive && !isNew" color="warn" mat-flat-button (click)="restoreItemDialog($event)">Restaurar</button>
      <button *ngIf="!isNew" type="button" color="accent" mat-flat-button (click)="clone()">Clonar</button>
      <button mat-flat-button type="button" class="btn btn-primary" color="cancel"
              routerLink="/searchSettings">Cancelar
      </button>
      <button mat-flat-button type="submit" class="btn btn-primary" color="save">Salvar</button>
      <button type="button" style="background-color:slateblue" mat-flat-button (click)="showSaveAndPublishDialog($event)">Salvar e Publicar</button>
    </div>
  </form>
</div>

