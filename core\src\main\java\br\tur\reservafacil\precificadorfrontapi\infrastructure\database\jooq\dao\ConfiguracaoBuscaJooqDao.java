package br.tur.reservafacil.precificadorfrontapi.infrastructure.database.jooq.dao;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoBuscaRecord;
import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoBusca;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoBuscaCiasExcluidas;
import br.tur.reservafacil.dominio.aereo.tipo.OrigemNotificacao;
import br.tur.reservafacil.dominio.tipo.NacInt;
import br.tur.reservafacil.dominio.tipo.SistEmis;
import br.tur.reservafacil.dominio.tipo.TipoOperador;
import br.tur.reservafacil.dominio.tipo.TipoRestricao;
import br.tur.reservafacil.precificadorfrontapi.domain.repository.*;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.database.jooq.dao.mapper.configuracaoBusca.ConfigBuscaEagerMapper;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.database.jooq.dao.mapper.configuracaoBusca.ConfigBuscaNotificacaoEagerMapper;
import br.tur.reservafacil.precificadorfrontapi.infrastructure.database.jooq.dao.mapper.configuracaoBusca.ConfiguracaoBuscaComAcordoEProdutosMapper;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.Tables.CODIGO_CONTRATO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.Tables.GRUPO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.AcordoComercial.ACORDO_COMERCIAL;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.AcordoComercialCiaAerea.ACORDO_COMERCIAL_CIA_AEREA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.CiaAerea.CIA_AEREA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoBusca.CONFIGURACAO_BUSCA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoBuscaAcordoComercial.CONFIGURACAO_BUSCA_ACORDO_COMERCIAL;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoBuscaFilial.CONFIGURACAO_BUSCA_FILIAL;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoBuscaProduto.CONFIGURACAO_BUSCA_PRODUTO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoBuscaRestricao.CONFIGURACAO_BUSCA_RESTRICAO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Credencial.CREDENCIAL;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Empresa.EMPRESA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Filial.FILIAL;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Notificacao.NOTIFICACAO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Produto.PRODUTO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Restricao.RESTRICAO;
import static br.tur.reservafacil.dominio.tipo.StatusPublicacao.*;
import static org.apache.commons.collections.CollectionUtils.isEmpty;

/**
 * Created by davidson on 4/26/16.
 */
@Repository
public class ConfiguracaoBuscaJooqDao extends AbstractConfiguracaoVersionavelJooqDao<ConfiguracaoBuscaRecord, ConfiguracaoBusca, Integer> implements ConfiguracaoBuscaRepository {

    public static final  String PARAM_CIA              = "ciaAerea";
    public static final  String PARAM_EMPRESA          = "empresa";
    public static final  String PARAM_PRODUTO          = "produto";
    public static final  String PARAM_NOME             = "nome";
    public static final  String PARAM_ATIVO            = "ativo";
    public static final  String PARAM_INATIVO          = "inativo";
    public static final  String PARAM_STATUS           = "status";
    public static final  String PARAM_NAC_INT          = "nacInt";
    public static final  String PARAM_FILIAL           = "filial";
    public static final  String DEFAULT_FIELD_ORDER_BY = "prioridade";
    public static final  String PARAM_EDITAVEL         = "editavel";
    public static final  String PARAM_AGENCIA          = "agencia";
    public static final  String PARAM_GRUPO            = "grupo";
    public static final  String PARAM_EMPRESA_ID       = "empresaId";
    public static final  String PARAM_FILIAL_ID        = "filialId";
    public static final  String PARAM_AGENCIA_ID       = "agenciaId";
    public static final  String PARAM_GRUPO_ID         = "grupoId";
    public static final  String PARAM_OID			   = "officeId";
    public static final  String PARAM_COD_CONTRATO     = "codContrato";

    @Autowired
    private ConfigBuscaEagerMapper configBuscaEagerMapper;

    @Autowired
    private ConfigBuscaNotificacaoEagerMapper configBuscaNotificacaoEagerMapper;

    @Autowired
    private AcordoComercialRepository acordoComercialRepository;

    @Autowired
    private ConfiguracaoBuscaCiasExcluidasRepository ciasExcluidasRepository;

    @Autowired
    private FilialRepository filialRepository;

    @Autowired
    private ProdutoRepository produtoRepository;

    @Autowired
    private ConfiguracaoBuscaComAcordoEProdutosMapper configuracaoBuscaComAcordoEProdutosMapper;

    @Autowired
    public ConfiguracaoBuscaJooqDao(Configuration configuration, DSLContext context) {
	super(CONFIGURACAO_BUSCA, ConfiguracaoBusca.class, configuration, context);
    }

    @Override
    protected Integer getId(ConfiguracaoBusca configuracaoBusca) {
	return configuracaoBusca.getId();
    }

    public ConfiguracaoBusca findByPkEager(Integer id) {
	List<SelectField<?>> fields = new ArrayList<>();
	fields.addAll(Arrays.asList(CONFIGURACAO_BUSCA.fields()));
	fields.addAll(Arrays.asList(RESTRICAO.fields()));

	ConfiguracaoBusca configuracaoBusca =
			this.configBuscaEagerMapper.map(this.getContext().select()
									.from(CONFIGURACAO_BUSCA)
									.leftJoin(CONFIGURACAO_BUSCA_RESTRICAO)
									.on(CONFIGURACAO_BUSCA.ID.eq(CONFIGURACAO_BUSCA_RESTRICAO.CONFIGURACAO_BUSCA_ID))
									.leftJoin(RESTRICAO)
									.on(CONFIGURACAO_BUSCA_RESTRICAO.RESTRICAO_ID.eq(RESTRICAO.ID))
									.where(CONFIGURACAO_BUSCA.ID.eq(id))
									.fetch()).get(0);
	preencheDadosConfiguracao(configuracaoBusca);
	return configuracaoBusca;
    }

    @Override
    public List<ConfiguracaoBusca> findEagerByKeys(Set<Integer> idsConfiguracoesPrecificacao) {
	return Optional.ofNullable(
			idsConfiguracoesPrecificacao
					.stream()
					.map(this::findByPkEager)
					.collect(Collectors.toList())
			).orElse(Collections.emptyList());
    }

    private void preencheDadosConfiguracao(ConfiguracaoBusca configuracaoBusca) {
	//Isso é feito para não ter que fazer um cartesiando de n restrições X n acordos X Cias e assim por diante
	configuracaoBusca.setAcordosComerciais(this.acordoComercialRepository.findAcordosByConfigId(configuracaoBusca.getId()));
	configuracaoBusca.setCiasExcluidas(this.getCiaAereasExcluidas(configuracaoBusca));
	configuracaoBusca.setFiliais(this.filialRepository.findByConfiguracaoBuscaId(configuracaoBusca.getId()));
	configuracaoBusca.setProdutos(this.produtoRepository.buscaProdutosDaConfiguracaoDeBusca(configuracaoBusca.getId()));
    }

    private List<ConfiguracaoBuscaCiasExcluidas> getCiaAereasExcluidas(ConfiguracaoBusca configuracaoBusca) {
	return ciasExcluidasRepository.findByConfiguracaoBuscaId(configuracaoBusca.getId());
    }

    /**
     * Devolve as configurações de busca com todas as informações preenchidas
     *
     * OBS: Esse método é pesado, pois busca muitas informações do banco de dados e caso esteja utilizando muito recurso talves seja necessário
     * trabalhar com cursor ao invés de bater varias vezes no banco.
     *
     * @param params os parametros a serem filtrados
     * @return um {@link List} de {@link ConfiguracaoBusca}.
     */
    @Override
    public List<ConfiguracaoBusca> findEagerByParameters(Map<String, String> params) {
	final SelectJoinStep<Record> selectJoinStep = this.getContext().select()
			.from(CONFIGURACAO_BUSCA)
			.leftJoin(CONFIGURACAO_BUSCA_RESTRICAO)
			.on(CONFIGURACAO_BUSCA.ID.eq(CONFIGURACAO_BUSCA_RESTRICAO.CONFIGURACAO_BUSCA_ID))
			.leftJoin(RESTRICAO)
			.on(CONFIGURACAO_BUSCA_RESTRICAO.RESTRICAO_ID.eq(RESTRICAO.ID));
	final SelectConditionStep<Record> queryByParameters = this.createQueryByParameters(selectJoinStep, params);
	queryByParameters.orderBy(createOrderBy("nome"));
	Result<Record> result = queryByParameters.fetch();

	List<ConfiguracaoBusca> configuracoes = this.configBuscaEagerMapper.map(result);
	configuracoes.stream().forEach(this::preencheDadosConfiguracao);
	return configuracoes;
    }

    // FIXME criar abstração para o AbstractJooqDao
    @Override public ConfiguracaoBusca ultimoPublicadoPorIdOrigem(Integer idOrigem) {
        return this.mapper().map(this.getContext()
                                                 .selectFrom(CONFIGURACAO_BUSCA)
                                                 .where(CONFIGURACAO_BUSCA.STATUS_PUBLICACAO.eq(PUBLICADA.name()))
                                                 .and(CONFIGURACAO_BUSCA.ID_ORIGEM.eq(idOrigem).or(CONFIGURACAO_BUSCA.ID.eq(idOrigem))).fetchOne());
    }

    // FIXME criar abstração para o AbstractJooqDao
    @Override public Integer ultimoEditavelPorIdOrigem(Integer idOrigem) {
        Record1<Integer> idOrigemRecord = this.getContext().select(CONFIGURACAO_BUSCA.ID).from(CONFIGURACAO_BUSCA).where(CONFIGURACAO_BUSCA.EDITAVEL.isTrue()).and(CONFIGURACAO_BUSCA.ID_ORIGEM.eq(idOrigem)).orderBy(CONFIGURACAO_BUSCA.ID.desc()).limit(1).fetchOne();
        return idOrigemRecord != null ? idOrigemRecord.value1() : null;
    }

    @Override
    public List<ConfiguracaoBusca> findConfiguracaoBuscaPorGrupoEEmpresaESistEmisProduto(String grupo, String empresa, SistEmis sistEmis, String produto) {
        List<SelectField<?>> fields = new ArrayList<>();
        fields.addAll(Arrays.asList(CONFIGURACAO_BUSCA.fields()));
        fields.addAll(Arrays.asList(ACORDO_COMERCIAL.fields()));
        fields.addAll(Arrays.asList(PRODUTO.fields()));
        SelectConditionStep<Record>
		        selectConditionStep =
                        this.getContext().select(fields)
		            .from(CONFIGURACAO_BUSCA)
		            .innerJoin(EMPRESA)
		            .on(EMPRESA.ID.eq(CONFIGURACAO_BUSCA.EMPRESA_ID))
		            .innerJoin(GRUPO)
		            .on(GRUPO.EMPRESA_ID.eq(EMPRESA.ID))
		            .innerJoin(CONFIGURACAO_BUSCA_PRODUTO)
		            .on(CONFIGURACAO_BUSCA_PRODUTO.CONFIGURACAO_BUSCA_ID.eq(CONFIGURACAO_BUSCA.ID))
		            .innerJoin(PRODUTO)
		            .on(CONFIGURACAO_BUSCA_PRODUTO.PRODUTO_ID.eq(PRODUTO.ID))
		            .innerJoin(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL)
		            .on(CONFIGURACAO_BUSCA.ID.eq(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL.CONFIGURACAO_BUSCA_ID))
		            .innerJoin(ACORDO_COMERCIAL)
		            .on(ACORDO_COMERCIAL.ID.eq(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL.ACORDO_COMERCIAL_ID))
		            .innerJoin(CREDENCIAL)
		            .on(CREDENCIAL.ID.eq(ACORDO_COMERCIAL.CREDENCIAL_ID))
		            .where(GRUPO.REFERENCIA.eq(grupo))
		            .and(EMPRESA.REFERENCIA.eq(empresa))
			    .and(PRODUTO.NOME.eq(produto))
		            .and(CREDENCIAL.SIST_EMIS.eq(sistEmis.getValue().toUpperCase()))
		            .and(CREDENCIAL.ATIVO.eq(Boolean.TRUE))
		            .and(CONFIGURACAO_BUSCA.ATIVO.eq(Boolean.TRUE))
		            .and(EMPRESA.ATIVO.eq(Boolean.TRUE))
		            .and(ACORDO_COMERCIAL.ATIVO.eq(Boolean.TRUE));


        Result<Record> result = selectConditionStep.fetch();
        return CollectionUtils.isEmpty(result) ? Collections.emptyList() :
		        configuracaoBuscaComAcordoEProdutosMapper.map(result);
    }

    @Override
    public List<ConfiguracaoBusca> findConfiguracaoBuscaPorGrupoEEmpresaCiaProduto(String grupo, String empresa, String cia, String produto) {
        List<SelectField<?>> fields = new ArrayList<>();
        fields.addAll(Arrays.asList(CONFIGURACAO_BUSCA.fields()));
        fields.addAll(Arrays.asList(ACORDO_COMERCIAL.fields()));
        fields.addAll(Arrays.asList(PRODUTO.fields()));
        SelectConditionStep<Record>
		        selectConditionStep =
		        this.getContext().select(fields)
		                .from(CONFIGURACAO_BUSCA)
		                .innerJoin(EMPRESA)
		                .on(CONFIGURACAO_BUSCA.EMPRESA_ID.eq(EMPRESA.ID))
		                .innerJoin(GRUPO)
		                .on(EMPRESA.ID.eq(GRUPO.EMPRESA_ID))
		                .leftJoin(CONFIGURACAO_BUSCA_PRODUTO)
		                .on(CONFIGURACAO_BUSCA.ID.eq(CONFIGURACAO_BUSCA_PRODUTO.CONFIGURACAO_BUSCA_ID))
		                .innerJoin(PRODUTO)
		                .on(CONFIGURACAO_BUSCA_PRODUTO.PRODUTO_ID.eq(PRODUTO.ID))
		                .innerJoin(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL)
		                .on(CONFIGURACAO_BUSCA.ID.eq(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL.CONFIGURACAO_BUSCA_ID))
		                .innerJoin(ACORDO_COMERCIAL)
		                .on(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL.ACORDO_COMERCIAL_ID.eq(ACORDO_COMERCIAL.ID))
		                .innerJoin(ACORDO_COMERCIAL_CIA_AEREA)
		                .on(ACORDO_COMERCIAL.ID.eq(ACORDO_COMERCIAL_CIA_AEREA.ACORDO_COMERCIAL_ID))
		                .innerJoin(CIA_AEREA)
		                .on(ACORDO_COMERCIAL_CIA_AEREA.CIA_AEREA_ID.eq(CIA_AEREA.ID))
		                .where(GRUPO.REFERENCIA.eq(grupo))
		                .and(EMPRESA.REFERENCIA.eq(empresa))
				.and(PRODUTO.NOME.eq(produto))
				.and(CIA_AEREA.CODIGO.eq(cia))
		                .and(CONFIGURACAO_BUSCA.ATIVO.eq(Boolean.TRUE))
		                .and(EMPRESA.ATIVO.eq(Boolean.TRUE))
		                .and(ACORDO_COMERCIAL.ATIVO.eq(Boolean.TRUE));

        Result<Record> result = selectConditionStep.fetch();
        return CollectionUtils.isEmpty(result) ? Collections.emptyList() :
		        configuracaoBuscaComAcordoEProdutosMapper.map(result);
    }

    @Override
    public Integer findIdConfiguracaoEditavelByIdOrigem(Integer idOrigem) {
	Record1<Integer> idOrigemRecord = this.getContext().select(CONFIGURACAO_BUSCA.ID)
			.from(CONFIGURACAO_BUSCA)
			.where(CONFIGURACAO_BUSCA.EDITAVEL.isTrue())
			.and(CONFIGURACAO_BUSCA.ID_ORIGEM.eq(idOrigem))
			.fetchOne();
	return idOrigemRecord != null ? idOrigemRecord.value1() : null;
    }

	@Override
	public Integer getCurrentVersionByIdOrigem(Integer idOrigem) {
		return this.getContext().select(DSL.coalesce(CONFIGURACAO_BUSCA.VERSAO.max(), 0))
				.from(CONFIGURACAO_BUSCA)
				.where(CONFIGURACAO_BUSCA.ID_ORIGEM.eq(idOrigem).or(CONFIGURACAO_BUSCA.ID.eq(idOrigem))).fetch(0, Integer.class)
				.stream().findFirst().orElse(0);
	}


    @Override
    public Integer countByParameters(Map<String, String> params) {
		SelectOnConditionStep<Record1<Integer>> query = getContext().selectDistinct(CONFIGURACAO_BUSCA.ID)
				.from(CONFIGURACAO_BUSCA)
				.innerJoin(EMPRESA).on(EMPRESA.ID.eq(CONFIGURACAO_BUSCA.EMPRESA_ID))
				.leftJoin(CONFIGURACAO_BUSCA_RESTRICAO).on(CONFIGURACAO_BUSCA_RESTRICAO.CONFIGURACAO_BUSCA_ID.eq(CONFIGURACAO_BUSCA.ID))
				.leftJoin(RESTRICAO).on(CONFIGURACAO_BUSCA_RESTRICAO.RESTRICAO_ID.eq(RESTRICAO.ID));

		if (params.containsKey(PARAM_OID) || params.containsKey(PARAM_COD_CONTRATO)) {
			query.join(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL)
					.on(CONFIGURACAO_BUSCA.ID.eq(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL.CONFIGURACAO_BUSCA_ID))
					.join(ACORDO_COMERCIAL)
					.on(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL.ACORDO_COMERCIAL_ID.eq(ACORDO_COMERCIAL.ID))
					.join(CODIGO_CONTRATO)
					.on(ACORDO_COMERCIAL.CODIGO_CONTRATO_ID.eq(CODIGO_CONTRATO.ID));
		}

        SelectConditionStep<Record1<Integer>> countQuery = query.where(createConditionsByParameters(params));

        return getContext().fetchCount(countQuery);
    }

    @Override
    public List<ConfiguracaoBusca> buscaConfiguracoesDeBuscaParaNotificacao() {
        Field<?>[] fields = ArrayUtils.addAll(CONFIGURACAO_BUSCA.fields(), RESTRICAO.fields());
        Field<?>[] allFields = ArrayUtils.addAll(fields, ACORDO_COMERCIAL.fields());
        return this.configBuscaNotificacaoEagerMapper.map(this.getContext().select(allFields)
		        .from(CONFIGURACAO_BUSCA)
		        .leftJoin(CONFIGURACAO_BUSCA_RESTRICAO)
				.on(CONFIGURACAO_BUSCA_RESTRICAO.CONFIGURACAO_BUSCA_ID.eq(CONFIGURACAO_BUSCA.ID))
		        .leftJoin(RESTRICAO)
				.on(CONFIGURACAO_BUSCA_RESTRICAO.RESTRICAO_ID.eq(RESTRICAO.ID)
					.and(RESTRICAO.TIPO_OPERADOR.in(Arrays.asList(TipoOperador.IGUAL, TipoOperador.ANTES, TipoOperador.ANTES_OU_IGUAL, TipoOperador.DENTRO)))
					.and(RESTRICAO.TIPO_RESTRICAO.like("DATA%").or(RESTRICAO.TIPO_RESTRICAO.like("PERIODO%"))))
		        .leftJoin(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL)
		                .on(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL.CONFIGURACAO_BUSCA_ID.eq(CONFIGURACAO_BUSCA.ID))
		        .leftJoin(ACORDO_COMERCIAL)
		                .on(ACORDO_COMERCIAL.ID.eq(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL.ACORDO_COMERCIAL_ID)
					.and(ACORDO_COMERCIAL.DATA_FIM_VIGENCIA.isNotNull()))
		        .where(CONFIGURACAO_BUSCA.STATUS_PUBLICACAO.eq(PUBLICADA.name()))
			.and(CONFIGURACAO_BUSCA.ATIVO.isTrue())
		        .and(CONFIGURACAO_BUSCA.ID.notIn(buscaNotificacoesDeConfiguracaoDeBuscaRecord()))
		        .and(RESTRICAO.ID.isNotNull().or(ACORDO_COMERCIAL.ID.isNotNull())).fetch());
    }

    @Override
    public List<ConfiguracaoBusca> findByKeys(Set<Integer> ids) {
        return this.getContext().select()
		        .from(CONFIGURACAO_BUSCA)
		        .where(CONFIGURACAO_BUSCA.ID.in(ids)).fetch().into(ConfiguracaoBusca.class);
    }

    protected SelectConditionStep<Record1<Integer>> buscaNotificacoesDeConfiguracaoDeBuscaRecord() {
	return this.getContext()
			.select(NOTIFICACAO.IDENTIFICADOR)
			.from(NOTIFICACAO)
			.where(NOTIFICACAO.ORIGEM_NOTIFICACAO.eq(OrigemNotificacao.CONFIGURACAO_BUSCA.name()));
    }

    public SelectConditionStep<Record> createQueryByParameters(SelectJoinStep<Record> selectJoin, Map<String, String> params){
	if(params.containsKey(PARAM_CIA) || params.containsKey(PARAM_OID) || params.containsKey(PARAM_COD_CONTRATO)) {
	    selectJoin.join(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL)
			    .on(CONFIGURACAO_BUSCA.ID.eq(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL.CONFIGURACAO_BUSCA_ID))
			    .join(ACORDO_COMERCIAL)
			    .on(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL.ACORDO_COMERCIAL_ID.eq(ACORDO_COMERCIAL.ID))
				.join(CODIGO_CONTRATO)
				.on(ACORDO_COMERCIAL.CODIGO_CONTRATO_ID.eq(CODIGO_CONTRATO.ID));
	}

	if(params.containsKey(PARAM_FILIAL_ID) || params.containsKey(PARAM_EMPRESA_ID)) {
	    selectJoin.join(EMPRESA).on(CONFIGURACAO_BUSCA.EMPRESA_ID.eq(EMPRESA.ID));
	    if(params.containsKey(PARAM_FILIAL_ID)) {
		selectJoin.join(FILIAL).on(FILIAL.EMPRESA_ID.eq(EMPRESA.ID));
	    }
	}

	List<Condition> conditions = this.createConditionsByParameters(params);
	return selectJoin.where(conditions);
    }

    private void addOrderBy(SelectConditionStep<Record> selectConditionStep, String orderBy) {
	if(StringUtils.isEmpty(orderBy)) {
	    orderBy = DEFAULT_FIELD_ORDER_BY;
	}
	final Field<?> fieldOrderBy = CONFIGURACAO_BUSCA.field(orderBy);
	if(fieldOrderBy != null) {
	    selectConditionStep.orderBy(fieldOrderBy);
	}
    }

    private Field<?> createOrderBy(String orderBy) {
	if(StringUtils.isEmpty(orderBy)) {
	    orderBy = DEFAULT_FIELD_ORDER_BY;
	}
	return CONFIGURACAO_BUSCA.field(orderBy);
    }

    private List<Condition> createConditionsByParameters(Map<String, String> params) {
	List<Condition> conditions = new ArrayList<>();
        if(params.containsKey(PARAM_EMPRESA_ID)) {
	    conditions.add(CONFIGURACAO_BUSCA.EMPRESA_ID.cast(String.class).in(params.get(PARAM_EMPRESA_ID).split(",")));
        }
	if(params.containsKey(PARAM_PRODUTO)) {
		conditions.add(CONFIGURACAO_BUSCA.ID.in(
				this.getContext().select(CONFIGURACAO_BUSCA_PRODUTO.CONFIGURACAO_BUSCA_ID)
						.from(CONFIGURACAO_BUSCA_PRODUTO).join(PRODUTO).on(PRODUTO.ID.eq(CONFIGURACAO_BUSCA_PRODUTO.PRODUTO_ID))
						.where(PRODUTO.NOME.likeIgnoreCase(params.get(PARAM_PRODUTO))))
		);
	}
	if(params.containsKey(PARAM_NOME)) {
	    conditions.add(CONFIGURACAO_BUSCA.NOME.likeIgnoreCase("%"+params.get(PARAM_NOME)+"%"));
	}

        String status = params.get(PARAM_STATUS);
        if(StringUtils.isNotEmpty(status)) {
	    conditions.add(CONFIGURACAO_BUSCA.STATUS_PUBLICACAO.likeIgnoreCase(status));
	    if (EM_HISTORICO.name().equals(params.get(PARAM_STATUS))) {
		conditions.add(CONFIGURACAO_BUSCA.EDITAVEL.isFalse());
	    }
	}
	if (!EM_HISTORICO.name().equals(params.get(PARAM_STATUS)) && params.containsKey(PARAM_EDITAVEL)) {
	    conditions.add(CONFIGURACAO_BUSCA.EDITAVEL.eq(Boolean.valueOf(params.get(PARAM_EDITAVEL)))
				.or(CONFIGURACAO_BUSCA.STATUS_PUBLICACAO.eq(PUBLICADA.name())));
//		conditions.add(CONFIGURACAO_BUSCA.EDITAVEL.eq(Boolean.valueOf(params.get(PARAM_EDITAVEL))));
	}
	if(params.containsKey(PARAM_NAC_INT)) {
	    String nacInt = params.get(PARAM_NAC_INT);
	    final NacInt nacIntEnum = NacInt.valueOf(nacInt);
	    conditions.add(CONFIGURACAO_BUSCA.NAC_INT.eq(nacIntEnum.getKey()));
	}
	if(params.containsKey(PARAM_CIA)) {
	    conditions.add(CONFIGURACAO_BUSCA.ID.in(
			    this.getContext().select(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL.CONFIGURACAO_BUSCA_ID)
					    .from(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL)
					    .innerJoin(ACORDO_COMERCIAL).on(ACORDO_COMERCIAL.ID.eq(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL.ACORDO_COMERCIAL_ID))
					    .innerJoin(ACORDO_COMERCIAL_CIA_AEREA).on(ACORDO_COMERCIAL.ID.eq(ACORDO_COMERCIAL_CIA_AEREA.ACORDO_COMERCIAL_ID))
					    .innerJoin(CIA_AEREA).on(ACORDO_COMERCIAL_CIA_AEREA.CIA_AEREA_ID.eq(CIA_AEREA.ID))
	                    .where(CIA_AEREA.CODIGO.likeIgnoreCase("%"+params.get(PARAM_CIA)+"%"))
	    ));
	}

	String ativo = INATIVO.name().equals(status) ? PARAM_INATIVO : params.get(PARAM_ATIVO);
        if (StringUtils.isNotEmpty(ativo)) {
	    conditions.add(CONFIGURACAO_BUSCA.ATIVO.eq(PARAM_ATIVO.equals(ativo)));
        } else {
	    conditions.add(CONFIGURACAO_BUSCA.ATIVO.isTrue());
        }

        Condition conditionRestricaoFilial = getConditionRestricaoFilialByParams(params);
        if (conditionRestricaoFilial != null) {
	    conditions.add(conditionRestricaoFilial);
        }

        Condition conditionRestricaoAgencia = getConditionRestricaoAgenciaByParams(params);
        if (conditionRestricaoAgencia != null) {
	    conditions.add(conditionRestricaoAgencia);
        }

        Condition conditionRestricaoGrupo = getConditionRestricaoGrupoByParams(params);
        if (conditionRestricaoGrupo != null) {
	    conditions.add(conditionRestricaoGrupo);
        }

        if (params.containsKey(PARAM_OID)) {
        	conditions.add(ACORDO_COMERCIAL.OFFICE_ID_BUSCA.likeIgnoreCase(params.get(PARAM_OID)));
		}

        if (params.containsKey(PARAM_COD_CONTRATO)) {
        	conditions.add(CODIGO_CONTRATO.CODIGO.likeIgnoreCase(params.get(PARAM_COD_CONTRATO)));
		}

        return conditions;
    }

    private Condition getConditionRestricaoFilialByParams(Map<String, String> params) {
        Condition condition = null;
        if(params.containsKey(PARAM_FILIAL_ID)) {
            condition = CONFIGURACAO_BUSCA.ID.in(this.getContext().select(CONFIGURACAO_BUSCA_FILIAL.CONFIGURACAO_BUSCA_ID)
		                                                 .from(CONFIGURACAO_BUSCA_FILIAL)
		                                                 .innerJoin(FILIAL).on(FILIAL.ID.eq(CONFIGURACAO_BUSCA_FILIAL.FILIAL_ID))
		                                                 .where(FILIAL.ID.cast(String.class).eq(params.get(PARAM_FILIAL_ID))));
//		            .or( CONFIGURACAO_BUSCA.ID.notIn(this.getContext().select(CONFIGURACAO_BUSCA_FILIAL.CONFIGURACAO_BUSCA_ID)
//				                                             .from(CONFIGURACAO_BUSCA_FILIAL)
//				                                             .innerJoin(FILIAL).on(FILIAL.ID.eq(CONFIGURACAO_BUSCA_FILIAL.FILIAL_ID))));
        }
        return condition;
    }

    private Condition getConditionRestricaoAgenciaByParams(Map<String, String> params) {
        Condition condition = null;
        if (params.containsKey(PARAM_AGENCIA)) {
            condition = CONFIGURACAO_BUSCA.ID.in(this.getContext().select(CONFIGURACAO_BUSCA_RESTRICAO.CONFIGURACAO_BUSCA_ID)
		                                                 .from(CONFIGURACAO_BUSCA_RESTRICAO)
		                                                 .innerJoin(RESTRICAO).on(RESTRICAO.ID.eq(CONFIGURACAO_BUSCA_RESTRICAO.RESTRICAO_ID))
		                                                 .where(RESTRICAO.TIPO_RESTRICAO.eq(TipoRestricao.AGENCIA.name())
				                                                        .and(RESTRICAO.VALOR.eq(params.get(PARAM_AGENCIA)))));
//		            .or(CONFIGURACAO_BUSCA.ID.notIn(this.getContext().select(CONFIGURACAO_BUSCA_RESTRICAO.CONFIGURACAO_BUSCA_ID)
//				                                            .from(CONFIGURACAO_BUSCA_RESTRICAO)
//				                                            .innerJoin(RESTRICAO).on(RESTRICAO.ID.eq(CONFIGURACAO_BUSCA_RESTRICAO.RESTRICAO_ID))
//				                                            .where(RESTRICAO.TIPO_RESTRICAO.eq(TipoRestricao.AGENCIA.name()))));
        }
        return condition;
    }

    private Condition getConditionRestricaoGrupoByParams(Map<String, String> params) {
        Condition condition = null;
        if (params.containsKey(PARAM_GRUPO)) {
            condition = CONFIGURACAO_BUSCA.ID.in(this.getContext().select(CONFIGURACAO_BUSCA_RESTRICAO.CONFIGURACAO_BUSCA_ID)
		                                                 .from(CONFIGURACAO_BUSCA_RESTRICAO)
		                                                 .innerJoin(RESTRICAO).on(RESTRICAO.ID.eq(CONFIGURACAO_BUSCA_RESTRICAO.RESTRICAO_ID))
		                                                 .where(RESTRICAO.TIPO_RESTRICAO.eq(TipoRestricao.GRUPO.name())
				                                                        .and(RESTRICAO.VALOR.eq(params.get(PARAM_GRUPO)))));
//		            .or(CONFIGURACAO_BUSCA.ID.notIn(this.getContext().select(CONFIGURACAO_BUSCA_RESTRICAO.CONFIGURACAO_BUSCA_ID)
//				                                            .from(CONFIGURACAO_BUSCA_RESTRICAO)
//				                                            .innerJoin(RESTRICAO).on(RESTRICAO.ID.eq(CONFIGURACAO_BUSCA_RESTRICAO.RESTRICAO_ID))
//				                                            .where(RESTRICAO.TIPO_RESTRICAO.eq(TipoRestricao.GRUPO.name()))));
        }
        return condition;
    }

    @Override
    public List<ConfiguracaoBusca> findConfiguracaoSoComRestricaoByEmpresa(Empresa empresa) {
        ArrayList<SelectField<?>> fields = new ArrayList<>();
        fields.addAll(Arrays.asList(CONFIGURACAO_BUSCA.fields()));
        fields.addAll(Arrays.asList(RESTRICAO.fields()));

        SelectOnConditionStep<Record> selectJoin =
		        this.getContext().select(fields)
		        .from(CONFIGURACAO_BUSCA)
		        .innerJoin(EMPRESA)
		            .on(CONFIGURACAO_BUSCA.EMPRESA_ID.eq(EMPRESA.ID))
		        .leftJoin(CONFIGURACAO_BUSCA_RESTRICAO)
		            .on(CONFIGURACAO_BUSCA.ID.eq(CONFIGURACAO_BUSCA_RESTRICAO.CONFIGURACAO_BUSCA_ID))
		        .leftJoin(RESTRICAO)
		            .on(CONFIGURACAO_BUSCA_RESTRICAO.RESTRICAO_ID.eq(RESTRICAO.ID));

        SelectConditionStep<Record> selectCondition = selectJoin.where(CONFIGURACAO_BUSCA.STATUS_PUBLICACAO.equalIgnoreCase(PUBLICADA.name()))
		        .and(CONFIGURACAO_BUSCA.ATIVO.isTrue())
		        .and(EMPRESA.ID.eq(empresa.getId()));

        return this.configBuscaEagerMapper.map(selectCondition.orderBy(CONFIGURACAO_BUSCA.PRIORIDADE).fetch());
    }

    public List<ConfiguracaoBusca> findConfiguracaoSoComRestricaoByEmpresaCiaSisteEmisProduto(Empresa empresa, Collection<String> cias,
                                                                                              Collection<SistEmis> sistEmis, String produto,
                                                                                              Boolean isBuscaDeTeste) {
	List<SelectField<?>> fields = new ArrayList<>();
	fields.addAll(Arrays.asList(CONFIGURACAO_BUSCA.fields()));
	fields.addAll(Arrays.asList(RESTRICAO.fields()));

        SelectOnConditionStep<Record> selectJoin = this.getContext().select(fields)
                                               .from(CONFIGURACAO_BUSCA)
                                               .leftJoin(CONFIGURACAO_BUSCA_RESTRICAO)
                                               .on(CONFIGURACAO_BUSCA.ID.eq(CONFIGURACAO_BUSCA_RESTRICAO.CONFIGURACAO_BUSCA_ID))
                                               .leftJoin(RESTRICAO)
                                               .on(CONFIGURACAO_BUSCA_RESTRICAO.RESTRICAO_ID.eq(RESTRICAO.ID))
                                               .leftJoin(EMPRESA)
                                               .on(CONFIGURACAO_BUSCA.EMPRESA_ID.eq(EMPRESA.ID));

	SelectConditionStep<Record> selectCondition = selectJoin.where(CONFIGURACAO_BUSCA.EMPRESA_ID.eq(empresa.getId()))
			.and(CONFIGURACAO_BUSCA.ATIVO.isTrue())
			.and(CONFIGURACAO_BUSCA.ID.in(this.getSubSelectAcordosComerciais(cias, sistEmis)));

        if(!StringUtils.isEmpty(produto)) {
            selectCondition.and(CONFIGURACAO_BUSCA.ID.in(this.getSubSelectProduto(produto))
		                                .or(CONFIGURACAO_BUSCA.ID.notIn(this.getSubSelectConfiguracoesComProdutosCadastrados())));
//            selectCondition.or(CONFIGURACAO_BUSCA.ID.notIn(this.getSubSelectConfiguracoesComProdutosCadastrados()));
        }

	if (isBuscaDeTeste != null && isBuscaDeTeste) {
	    selectCondition.and(CONFIGURACAO_BUSCA.EDITAVEL.isTrue());
	} else {
	    selectCondition.and(CONFIGURACAO_BUSCA.STATUS_PUBLICACAO.equalIgnoreCase(PUBLICADA.name()));
	}

	return this.configBuscaEagerMapper.map(selectCondition.orderBy(CONFIGURACAO_BUSCA.PRIORIDADE).fetch());
    }

    private SelectConditionStep getSubSelectConfiguracoesComProdutosCadastrados() {
        /** A CONDICAO CRIADA (WHERE) É APENAS PARA DEVOLVER O TIPO CERTO QUE O MÉTODO ESPERA.
         * ESSE SELECT É A MESMA COISA QUE "SELECT CONFIGURACAO_BUSCA_ID FROM CONFIGURACAO_BUSCA_PRODUTO" */
        return this.getContext()
		        .select(CONFIGURACAO_BUSCA_PRODUTO.CONFIGURACAO_BUSCA_ID)
		        .from(CONFIGURACAO_BUSCA_PRODUTO)
		        .where(CONFIGURACAO_BUSCA_PRODUTO.ID.isNotNull());
    }

    private SelectConditionStep getSubSelectProduto(String produto) {
        return this.getContext()
		        .select(CONFIGURACAO_BUSCA_PRODUTO.CONFIGURACAO_BUSCA_ID)
		        .from(CONFIGURACAO_BUSCA_PRODUTO)
		        .join(PRODUTO)
		        .on(PRODUTO.ID.eq(CONFIGURACAO_BUSCA_PRODUTO.PRODUTO_ID))
		        .where(PRODUTO.NOME.eq(produto));
    }
    @Override
    public List<ConfiguracaoBusca> findByParameters(int page, int pageSize, String orderBy, Map<String, String> params) {
	final SelectJoinStep<Record> selectJoinStep = this.getContext().selectDistinct(CONFIGURACAO_BUSCA.fields()).from(CONFIGURACAO_BUSCA);
	final SelectConditionStep<Record> queryByParameters = this.createQueryByParameters(selectJoinStep, params);

	SelectForUpdateStep<Record> selectLimitado = queryByParameters.limit(pageSize).offset((page - 1) * pageSize);

	this.addOrderBy(queryByParameters, orderBy);

	SelectOnConditionStep<Record> records =
		this.getContext().select(selectLimitado.asTable(CONFIGURACAO_BUSCA.getName()).fields()).select(
			RESTRICAO.fields()).from(selectLimitado.asTable(CONFIGURACAO_BUSCA.getName())).leftJoin(
			CONFIGURACAO_BUSCA_RESTRICAO).on(CONFIGURACAO_BUSCA_RESTRICAO.CONFIGURACAO_BUSCA_ID.eq(
			selectLimitado.asTable(CONFIGURACAO_BUSCA.getName()).field(CONFIGURACAO_BUSCA.ID))).leftJoin(
			RESTRICAO).on(CONFIGURACAO_BUSCA_RESTRICAO.RESTRICAO_ID.eq(RESTRICAO.ID));

	return this.configBuscaEagerMapper.map(records.fetch());
    }

    private SelectOnConditionStep getSubSelectAcordosComerciais(Collection<String> cias, Collection<SistEmis> sistEmis) {
	//SELECT ConfiguracaoBuscaID FROM E JOINS
	final SelectOnConditionStep<Record1<Integer>> selectCondition = this.getContext()
			.select(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL.CONFIGURACAO_BUSCA_ID)
			.from(ACORDO_COMERCIAL)
			.join(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL)
			.on(CONFIGURACAO_BUSCA_ACORDO_COMERCIAL.ACORDO_COMERCIAL_ID.eq(ACORDO_COMERCIAL.ID));

	final boolean hasCias = !isEmpty(cias);
	final boolean hasSistEmis = !CollectionUtils.isEmpty(sistEmis);
	if(hasCias) {
	    selectCondition.join(ACORDO_COMERCIAL_CIA_AEREA).on(ACORDO_COMERCIAL_CIA_AEREA.ACORDO_COMERCIAL_ID.equal(ACORDO_COMERCIAL.ID));
	    selectCondition.join(CIA_AEREA).on(CIA_AEREA.ID.equal(ACORDO_COMERCIAL_CIA_AEREA.CIA_AEREA_ID));
	}
	if(hasSistEmis) {
	    selectCondition.join(CREDENCIAL).on(ACORDO_COMERCIAL.CREDENCIAL_ID.equal(CREDENCIAL.ID));
	}

	//WHERE
	selectCondition.where(this.criaCondicaoAcordosVigentesEAtivos());
	if(hasCias) {
	    selectCondition.and(CIA_AEREA.CODIGO.in(cias));
	}
	if(hasSistEmis) {
	    selectCondition.and(CREDENCIAL.SIST_EMIS.in(sistEmis));
	}
	return selectCondition;
    }

    private Condition criaCondicaoAcordosVigentesEAtivos() {
	Field<Timestamp> hoje = DSL.currentTimestamp();
	Condition inicio = ACORDO_COMERCIAL.DATA_INICIO_VIGENCIA.isNull().or(ACORDO_COMERCIAL.DATA_INICIO_VIGENCIA.lessOrEqual(hoje));
	Condition fim = ACORDO_COMERCIAL.DATA_FIM_VIGENCIA.isNull().or(ACORDO_COMERCIAL.DATA_FIM_VIGENCIA.greaterOrEqual(hoje));
	return inicio.and(fim).and(ACORDO_COMERCIAL.ATIVO.isTrue());
    }


    @Override
    protected ConfiguracaoBuscaRecord updateRecord(ConfiguracaoBusca value) {
	final ConfiguracaoBuscaRecord record = super.updateRecord(value);
//	record.changed(CONFIGURACAO_BUSCA.ID, false);
//	record.changed(CONFIGURACAO_BUSCA.NOME, false);
	record.changed(CONFIGURACAO_BUSCA.VERSAO, false);
	return record;
    }

    @Override
    public ConfiguracaoBusca ultimoNaoPublicadoPorIdOrigem(Integer idOrigem) {
        ConfiguracaoBuscaRecord record = this.getContext()
		        .selectFrom(CONFIGURACAO_BUSCA)
		        .where(CONFIGURACAO_BUSCA.STATUS_PUBLICACAO.eq(NAO_PUBLICADA.name()))
		        .and(CONFIGURACAO_BUSCA.ID_ORIGEM.eq(idOrigem).or(CONFIGURACAO_BUSCA.ID.eq(idOrigem)))
		        .fetchOne();
        if (record != null) {
	    return this.mapper().map(record);
        }
        return null;
    }

}
