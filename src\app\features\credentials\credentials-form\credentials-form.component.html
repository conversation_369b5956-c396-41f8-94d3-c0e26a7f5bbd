<app-simple-form
  listUrl="/credenciais"
  listName="Credenciais"
  [title]="title"
  titleNew="Nova credencial"
  apiUrl="credenciais"
  [formDefault]="formDefault"
  [value2FormValue]="value2FormValue"
  [formValue2Value]="formValue2Value"
  [isCloneable]="true"
>

  <ng-template #formTemplate let-getFormControl='getFormControl' let-form='form'>
    <div>
      <button mat-stroked-button (click)="validar($event)" style="float:right; margin-right: 15px;" matTooltip="Validar Credencial">
        <mat-icon>assignment_turned_in</mat-icon>
      </button>
    </div>
    <mat-card [formGroup]="form">
      <mat-card-content>

        <div fxLayout="row" fxLayoutGap="15px">
          <app-input-field
            fxFlex="55"
            label="Nome"
            name="nome"
            [maxlength]="50"
            [required]="true">
          </app-input-field>
          <app-input-field
            fxFlex="22"
            label="Iata"
            name="iata"
            [maxlength]="10">
          </app-input-field>
          <app-input-field
            fxFlex="23"
            label="Qtd de Recomendações "
            name="quantidadeRecomendacoes"
            [maxlength]="10">
          </app-input-field>
        </div>

        <div fxLayout="row" fxLayoutGap="15px">
          <app-search-box
            [formControl]="getFormControl('sistEmis')"
            resource="sistemis/json" displayField="nome"
            required="true"
            valueField="nome"
            [searchFields]="['nome']"
            placeholder="Sistema Emissor"
            fxFlex="50"
            valueAsValueField="true"
            [required]="true">
          </app-search-box>
          <app-input-field
            fxFlex="50"
            label="Código da empresa/IPCC"
            name="codigoEmpresa"
            [maxlength]="50"
          >
          </app-input-field>
        </div>

        <div fxLayout="row" fxLayoutGap="15px">
          <app-input-field
            fxFlex="50"
            label="Complemento"
            name="complemento"
            [maxlength]="200"
          >
          </app-input-field>
          <app-search-box formControlName="agencia" resource="agencias" displayField="nomeTratado"
                          valueField="id" [searchFields]="['nomeFantasia', 'referencia']" placeholder="Agência"
                          fxFlex="50">
          </app-search-box>
        </div>

        <div fxLayout="row" fxLayoutGap="15px">
          <app-search-box
            fxFlex="33"
            [formControl]="getFormControl('empresa')"
            resource="empresas"
            displayField="nomeTratado"
            valueField="id"
            placeholder="Empresa"
            [searchFields]="['referencia', 'nome']"
            [required]="true" (selectionChange)="empresaTransaction($event)">
          </app-search-box>
          <app-search-box fxFlex="33" formControlName="paisOrigem" resource="credenciais/paises-origem" displayField="nome"
                          valueField="codigo"
                          valueAsValueField="true" [searchFields]="['codigo','nome']" placeholder="País de Origem"
                          fxFlex="50">
          </app-search-box>
          <app-input-field
            fxFlex="50"
            label="Cod. Cia Aérea Padrão"
            name="defaultCarrier"
            maxlength="2">
          </app-input-field>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="printer" [formGroup]="form">
      <label class="tituloImpressora">Impressora</label>
      <mat-card-content>
        <div fxLayout="row" fxLayoutGap="15px">
          <app-input-field
            fxFlex="33"
            label="Itin"
            name="printeritin"
            [maxlength]="12"
          >
          </app-input-field>
          <app-input-field
            fxFlex="33"
            label="Misc"
            name="printermisc"
            [maxlength]="12"
          >
          </app-input-field>
          <app-input-field
            fxFlex="33"
            label="Tkt"
            name="printertkt"
            [maxlength]="20"
          >
          </app-input-field>
        </div>
      </mat-card-content>
    </mat-card>


    <mat-card class="autenticacao" [formGroup]="form">
      <div>
        <label class="tituloAutenticação">Autenticação</label>
        <button mat-stroked-button (click)="editar($event, form)" style="float: right">
          <mat-icon>{{isPasswordRequired ? 'lock_open' : 'lock'}}</mat-icon>
        </button>
      </div>
      <mat-card-content>
        <div fxLayout="row" fxLayoutGap="15px">
          <app-search-box fxFlex="10"
                          formControlName="protocolo"
                          [options]="[{id:'http', nome: 'HTTP'},
                                    {id:'https', nome: 'HTTPS'}]"
                          displayField="nome"
                          valueField="id"
                          valueAsValueField="true"
                          placeholder="Protocolo"
                          [maxlength]="5"
                          [required]="true"
          >
          </app-search-box>
          <app-input-field
            fxFlex="50"
            label="Host"
            name="host"
            [maxlength]="200"
            [required]="true"
          >
          </app-input-field>
          <app-input-field
            fxFlex="10"
            label="Porta"
            name="porta"
            [maxlength]="5"
            [required]="true"
          >
          </app-input-field>
          <app-input-field
            fxFlex="30"
            label="Proxy Provider"
            name="proxyProvider"
          >
          </app-input-field>
        </div>
        <div fxLayout="row" fxLayoutGap="15px">
          <app-input-field
            fxFlex="100"
            label="Caminho"
            name="caminho"
            [maxlength]="100"
          >
          </app-input-field>
        </div>
        <div fxLayout="row" fxLayoutGap="15px">
          <app-input-field
            fxFlex="100"
            label="ApiKey"
            name="apiKey"
          >
          </app-input-field>
        </div>

        <div fxLayout="row" fxLayoutGap="15px">
          <app-input-field
            fxFlex="100"
            label="Usuário"
            name="usuario"
            [maxlength]="50"
            [required]="true"
          >
          </app-input-field>

          <app-input-field
            fxFlex="100"
            label="Senha"
            name="senha"
            [maxlength]="128"
            [required]="isPasswordRequired"
            *ngIf="isNew || isPasswordRequired">
          </app-input-field>
        </div>

      </mat-card-content>
    </mat-card>

    <mat-card class="autenticacao" *ngIf="!isNew">
      <mat-card-title >Acordos Comerciais</mat-card-title>
        <mat-card-content>
          <div fxLayout="row" fxLayoutGap="15px" style="margin: 1em 0">
            <mat-card-content>
              <mat-chip-list>
                <mat-chip *ngFor="let acordo of data.acordosComerciais" >
                  <a class="config-link" (click)="customEventButtonToRouter($event, acordo.id)">
                    <span>{{acordo.nome}}</span>
                    <mat-icon [inline]="true">
                      open_in_new
                    </mat-icon>
                  </a>
                </mat-chip>
              </mat-chip-list>
            </mat-card-content>
          </div>
        </mat-card-content>
    </mat-card>
  </ng-template>

</app-simple-form>
