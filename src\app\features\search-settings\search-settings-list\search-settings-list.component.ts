import {Component, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {SimpleListComponent} from "../../../shared/components/simple-list/simple-list.component";
import {MatDialog} from "@angular/material/dialog";

@Component({
  selector: 'app-search-settings-list',
  templateUrl: './search-settings-list.component.html'
})
export class SearchSettingsListComponent {

  public formDefault = {
    nome: '',
    empresa: '',
    ciaAerea: '',
    statusRegistro: '',
    statusConfiguracao: '',
    nacInt: '',
    produto: '',
    filial: '',
    agencia: '',
    grupo: '',
    officeId: '',
    codContrato: '',
  };

  public initialFilter = {orderBy: 'prioridade'};

  @ViewChild('filterTemplate')
  public filterTemplate: TemplateRef<any>;

  @ViewChild('simpleList')
  public simpleList: SimpleListComponent;

  constructor(
    public dialog: MatDialog,) { }

  public profileReceived(profile: any, globalFilters: any) {
    return globalFilters;
  }

}
