<app-simple-form
  listUrl="/localidades"
  listName="Localidades"
  [title]="title"
  titleNew="Nova localidade"
  apiUrl="localidades"
  [formDefault]="formDefault"
  [value2FormValue]="value2FormValue"
  [formValue2Value]="formValue2Value"
>
  <ng-template
    #formTemplate
    let-getFormControl="getFormControl"
    let-form="form"
  >
    <mat-card [formGroup]="form">
      <mat-card-content>
        <div fxLayout="row" fxLayoutGap="15px">
          <app-airport-search-box
            fxFlex
            [formControl]="getFormControl('codigoIata')"
            placeholder="Código Iata"
            resource="aeroportos"
            displayField="codigo"
            valueField="id"
            [searchFields]="['codigo']"
            [required]="true"
          >
          </app-airport-search-box>
          <app-airport-search-box
            fxFlex
            [formControl]="getFormControl('country')"
            placeholder="Código País"
            resource="registrationCountry"
            displayField="codigo"
            valueField="id"
            [searchFields]="['codigo', 'descricao']"
            [required]="true"
            (selectionChange)="onCountryChange($event, form)"
          >
          </app-airport-search-box>
          <app-airport-search-box
            fxFlex
            [formControl]="getFormControl('state')"
            placeholder="Código Estado"
            resource="registrationState"
            displayField="codigo"
            valueField="id"
            [searchFields]="['codigo', 'descricao']"
            [required]="true"
            [filters]="stateFilters"
            (selectionChange)="onStateChange($event, form)"
          >
          </app-airport-search-box>
          <mat-form-field fxFlex hideRequiredMarker>
          <input
            matInput
            [formControl]="getFormControl('city')"
            placeholder="Código Cidade"
            required
            autocomplete="off"
            inputmode="numeric"/>
          <button
            mat-icon-button
            matSuffix
            color="warn"
            aria-label="Clear"
            type="button"
            (click)="getFormControl('city').reset()"
            *ngIf="getFormControl('city')?.value">
            <mat-icon>clear</mat-icon>
          </button>

          <mat-error>
            <app-error-msg label="Código Cidade"></app-error-msg>
          </mat-error>
        </mat-form-field>
        </div>
      </mat-card-content>
    </mat-card>
  </ng-template>
</app-simple-form>
