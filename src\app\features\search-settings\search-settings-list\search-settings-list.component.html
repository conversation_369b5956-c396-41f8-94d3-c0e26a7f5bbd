<app-simple-list #simpleList apiRoute="configuracoesBusca" name="searchSettings" [filterDialogComponent]="filterTemplate"
    [profileReceived]="profileReceived" [publishFeature]="true" [exportxls]="true" [initialFilter]="initialFilter"
    [ativoByDefault]="false" [queryParams]="{editavel: true}">
  <ng-template #listTemplate let-item="item">
    <div fxLayout="column" style="padding-left: 20px; color: grey">
      <small>Prioridade: {{item.prioridade}}</small>
      <span style="color: black;">{{ item.nome }}</span>
      <small *ngIf="item.peso> 0">Peso: {{ item.peso }}%</small>
      <small style="font-size:8pt;">{{item.restricoes | restriction}}</small>
    </div>
  </ng-template>

  <ng-template #filterTemplate let-data>
    <app-simple-filter-dialog [showStatusFilter]="false" [data]="data" [dialogClose]="data.dialogClose" [formDefault]="formDefault">
      <ng-template #filtersTemplate let-getFormControl='getFormControl'>
        <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="space-between center">
          <mat-form-field fxFlex="33">
            <mat-label>Nome</mat-label>
            <input matInput [formControl]="getFormControl('nome')" placeholder="Nome">
          </mat-form-field>
          <app-search-box resource="empresas" [searchFields]="['referencia', 'nome']" displayField="nomeTratado" [multiple]="true"
            [formControl]="getFormControl('empresa')" valueField="nome" placeholder="Empresa" fxFlex="33">
          </app-search-box>
          <app-search-box fxFlex="33" [formControl]="getFormControl('ciaAerea')" resource="ciasAereas/combo" displayField="nomeTratado"
            valueField="codigo" placeholder="Cia Aérea" [searchFields]="['codigo', 'nome']">
          </app-search-box>
        </div>

        <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="space-between center">
          <app-search-box fxFlex="33" [formControl]="getFormControl('statusRegistro')" displayField="nome" placeholder="Status do registro" valueField="id"
            [options]="[{id: 'ativo', nome: 'Ativo'},
                        {id: 'inativo', nome: 'Inativo'}]">
          </app-search-box>
          <app-search-box fxFlex="33" [formControl]="getFormControl('statusConfiguracao')" displayField="nome" placeholder="Status da configuração" valueField="id"
            [options]="[{id: 'NAO_PUBLICADA', nome: 'Não publicados'},
                        {id: 'PUBLICADA', nome: 'Publicados'},
                        {id: 'EM_HISTORICO', nome: 'Em Histórico'},
                        {id: 'INATIVO', nome: 'Inativos'}]">
          </app-search-box>
          <app-search-box fxFlex="33" [formControl]="getFormControl('nacInt')" displayField="nome" placeholder="Tipo de Vôo" valueField="id"
            [options]="[{id: 'NAC', nome: 'Nacional'},
                        {id: 'INT', nome: 'Internacional'}]">
          </app-search-box>
        </div>

        <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="space-between center">
          <app-search-box fxFlex="33" [formControl]="getFormControl('produto')" resource="produtos" displayField="nome"
            [searchFields]="['nome']" valueField="nome" placeholder="Produto"  [pageSize]="100" [useOrToFind]="false">
          </app-search-box>
          <div fxFlex="33"></div>
          <div fxFlex="33"></div>
        </div>

        <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="space-between center">
          <app-search-box [formControl]="getFormControl('filial')" resource="filiais" displayField="nome"
            valueField="nome" [searchFields]="['referencia', 'nome']" placeholder="Filial" fxFlex="33">
          </app-search-box>
          <app-search-box [formControl]="getFormControl('agencia')" resource="agencias" displayField="nomeTratado"
            valueField="id" [searchFields]="['nomeFantasia', 'referencia']" placeholder="Agência" fxFlex="33">
          </app-search-box>
          <app-search-box [formControl]="getFormControl('grupo')" resource="grupos" displayField="nomeTratado"
            [searchFields]="['referencia', 'nome']" valueField="id" placeholder="Grupos" fxFlex="33" >
          </app-search-box>
        </div>

        <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="space-between center">
          <mat-form-field fxFlex="50">
            <mat-label>Office Id Busca</mat-label>
            <input matInput [formControl]="getFormControl('officeId')" placeholder="Office Id Busca">
          </mat-form-field>
          <mat-form-field fxFlex="50">
            <mat-label>Código de contrato</mat-label>
            <input matInput [formControl]="getFormControl('codContrato')" placeholder="Código de contrato">
          </mat-form-field>
        </div>

      </ng-template>
    </app-simple-filter-dialog>
  </ng-template>

</app-simple-list>
