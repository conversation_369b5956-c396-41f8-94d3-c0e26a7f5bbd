.company-settings-container {
  overflow-y: auto;
  scroll-snap-type: y proximity;
  padding-top: 0;
  padding-bottom: 2em;
}

.config-link {
  display: flex;
  align-items: center;
}

.mn-card {
  margin-bottom: 2em;
  padding: 1em 0.8em 1em 1.4em;
}

.mat-card {
  background: none;
  width: 100%;
}

.form-field {
  margin-top: 1px;
  width: 100%;
  min-width: 300px;
}

.supscript {
  font-size: 8pt;
  font-weight: initial;
  color: white
}

.mat-icon {
  overflow: hidden;
}

.mat-column-emissor,
.mat-column-percentual {
  flex: 0 0 45%;
}

.mat-form-field {
  width: calc(100% - 15px);
}

.mat-column-acoes {
  flex: 0 0 10%;
}

.mat-row,
.mat-header-row,
.mat-footer-row {
  border-bottom-width: 0px;
}

.mat-progress-bar {
  height: 1px;
}

.mat-chip.mat-basic-chip {
  display: inline-flex;
  padding: 10px;
  border-radius: 16px;
  align-items: center;
  cursor: default;
  font-size: 12px;
  font-weight: normal;
  height: 20px;
}

.mat-chip.mat-basic-chip.active {
  background-color: black;
  color: white;
}

.mat-chip.mat-basic-chip.inactive {
  background-color: red;
  color: black;
}

a {
  text-decoration: none;
  display: inline;
  cursor: pointer;
}
.scroll{
  max-height: 500px;
  overflow-y: auto;
}

/* Estilos do Modal de Cadastro em Massa */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;

  h3 {
    margin: 0;
    color: #333;
  }
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
}

.form-section {
  margin-bottom: 24px;
}

.section-label {
  display: block;
  font-weight: 500;
  color: #666;
  margin-bottom: 12px;
  font-size: 14px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;

  > * {
    flex: 1;
  }
}

.chips-container {
  margin-top: 12px;

  .mat-chip-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
}

.info-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  margin: 8px 0;
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 4px;
  color: #1976d2;
  font-size: 14px;

  mat-icon {
    color: #1976d2;
    font-size: 20px;
    width: 20px;
    height: 20px;
  }
}