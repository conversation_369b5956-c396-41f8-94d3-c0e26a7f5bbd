import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RcSettingsFormComponent } from './rc-settings-form/rc-settings-form.component';
import {RcSettingsListComponent} from "./rc-settings-list/rc-settings-list.component";
import {RcSettingsRoutingModule} from "./rc-settings-routing.module";
import {SharedModule} from "../../shared/shared.module";
import {MatCardModule} from "@angular/material/card";
import {ReactiveFormsModule} from "@angular/forms";
import {FlexModule} from "@angular/flex-layout";
import {MaterialModule} from "../../material.module";
import { NgxObjectDiffModule } from 'ngx-object-diff';



@NgModule({
  declarations: [
    RcSettingsFormComponent,
    RcSettingsListComponent
  ],
    imports: [
        CommonModule,
        RcSettingsRoutingModule,
        SharedModule,
        MatCardModule,
        ReactiveFormsModule,
        FlexModule,
        NgxObjectDiffModule,
        MaterialModule
    ]
})
export class RcSettingsModule { }
