import {Component, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {SimpleListComponent} from "../../../shared/components/simple-list/simple-list.component";
import {MatDialog} from "@angular/material/dialog";

const API_ROUTE = 'configuracaoRc';

@Component({
  selector: 'app-rc-settings-list',
  templateUrl: './rc-settings-list.component.html',
})
export class RcSettingsListComponent {

  @ViewChild('simpleList')
  public simpleList: SimpleListComponent;

  @ViewChild('filterTemplate')
  public filterTemplate: TemplateRef<any>;

  public get apiRoute () {
    return API_ROUTE
  }

  public queryParams = {
    orderBy: "prioridade",
  }


  constructor(public dialog: MatDialog) { }

  public profileReceived(profile: any, globalFilters: any) {
    return globalFilters;
  }

}
