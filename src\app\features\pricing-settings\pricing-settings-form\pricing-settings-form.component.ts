import { Component, OnInit, ViewChild } from '@angular/core';
import { Form<PERSON>uilder, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Restangular } from 'ngx-restangular';
import { AuthService } from '../../../core/services/auth.service';
import { RedirectService } from '../../../core/services/redirect.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { SnackBarComponent } from '../../../shared/components/snack-bar/snack-bar.component';
import { DialogData } from '../../../shared/components/dialog/DialogData';
import { MatDialog } from '@angular/material/dialog';
import { DialogComponent } from '../../../shared/components/dialog/dialog.component';
import { SimpleHistoryComponent } from 'src/app/shared/components/simple-history/simple-history.component';
import { InputFieldComponent } from 'src/app/shared/components/input-field/input-field.component';
import {NgxObjectDiffService} from 'ngx-object-diff';
import { SimpleSubregistryComponent } from 'src/app/shared/components/simple-subregistry/simple-subregistry.component';

const CONFIGURACOES_COM_PRIORIDADE = ["RESTRITA", "PADRAO", "SUCESSOR"];


interface IMarkupProduto {

  id: number;
  idReference: number;
  produto: {
    id: number;
    nome: string;
    descricao: string;
    ativo: boolean;
  };
  configuracaoPrecificacaoId: number;
  markupBusca: number;
  markupMinimo: number;
  fee: number;
  comissaoCliente: number;
  incentivoCliente: number;
  repasseCliente: number;
  zeraDu: boolean;
  zeraMkpFornecedor: boolean;
  zeraTaxaBolsaRC: boolean;
  moedaRepasse: string;
  taxaBolsa: number;
  taxaBolsaPerc: number;
  taxaBolsaCHD: number;
  taxaBolsaINF: number;
  taxaBolsaPercCHD: number;
  taxaBolsaPercINF: number;
  codigoRcSica?: string,
  fatoresMap: {
    FEE: number;
    COMISSAO_CLIENTE: number;
    INCENTIVO_CLIENTE: number;
    MARKUP: number;
    REPASSE_CLIENTE: number;
  };
  markupEmFatorDivisao: number;
  taxaBolsaFixa: boolean;
}

@Component({
  selector: 'app-pricing-settings-form',
  templateUrl: './pricing-settings-form.component.html',
  styleUrls: ['./pricing-settings-form.component.scss'],
})
export class PricingSettingsFormComponent implements OnInit {
  @ViewChild('taxaBolsa')
  taxaBolsaControl: InputFieldComponent;

  @ViewChild('taxaBolsaPerc')
  taxaBolsaPercControl: InputFieldComponent;

  @ViewChild('taxaBolsaCHD')
  taxaBolsaCHDControl: InputFieldComponent;

  @ViewChild('taxaBolsaPercCHD')
  taxaBolsaPercCHDControl: InputFieldComponent;

  @ViewChild('taxaBolsaINF')
  taxaBolsaINFControl: InputFieldComponent;

  @ViewChild('taxaBolsaPercINF')
  taxaBolsaPercINFControl: InputFieldComponent;

  @ViewChild("formSubregistry")
  formSubregistry: SimpleSubregistryComponent

  public get isNew(): boolean {
    return !(this.currentId !== null && typeof this.currentId !== 'undefined');
  }

  public get getId(): number {
    return this.currentId;
  }

  public get isActive(): boolean {
    return this.active;
  }

  public get cloneId() {
    let cloneId = undefined;
    this.route.queryParams.subscribe((params) => {
      cloneId = params.cloneId;
    });
    return cloneId;
  }

  public loading: boolean = true;
  public active: boolean = undefined;
  public title: string;
  public linkedCommercialPricingSettings: any[];
  loadingSearch = false;
  protected readonly RESOURCE_NAME: string = 'configuracoesPrecificacao';
  private isFeedHistEmpty: boolean = true;

  private currentId: number = undefined;
  private initialized: boolean = false;
  pricingSettingsFormGroup: FormGroup;

  markupFormDefault = {
    produto: [],
    markupBusca: null,
    markupEmFatorDivisao: 0,
    markupMinimo: '',
    taxaBolsa: 0,
    taxaBolsaCHD: 0,
    taxaBolsaINF: 0,
    taxaBolsaPerc: 0,
    taxaBolsaPercCHD: 0,
    taxaBolsaPercINF: 0,
    codigoRcSica: '',
    moedaRepasse: null,
    fee: '',
    zeraDu: false,
    zeraMkpFornecedor: false,
    zeraTaxaBolsaRC: false,
  };

  data = {
    nome: null,
    empresas: null,
    prioridade: null,
    condicaoPrecificacao: null,
    restricoes: [],
    historico: [],
    comissaoFornecedor: null,
    incentivoFornecedor: null,
    tourcode: null,
    endosso: null,
    markups: [],
    ativo: null,
    tipoConfigPrecificacao: null,
    ciasAereas: [],
    emissaoOnline: null,
    tipoMarkup: null,
    editavel: null,
    statusPublicacao: null,
    idOrigem: null,
    idConfigEditavel: null,
    forcarEmissao: false,
    zerarRepasse: false,
    nacInt: null,
    id: null,
    versao: null,
    auditoria: null,
    osi: null
  };

  constructor(
    private _formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private restangular: Restangular,
    private snackBar: MatSnackBar,
    private authService: AuthService,
    private redirectService: RedirectService,
    private dialog: MatDialog,
    private objectDiff: NgxObjectDiffService

  ) {


  }

  public async ngOnInit() {
    this.pricingSettingsFormGroup = this._formBuilder.group({
      nome: [this.data ? this.data.nome : null],
      empresas: [this.data ? this.data.empresas : null],
      prioridade: [this.data ? this.data.prioridade : null],
      condicaoPrecificacao: [this.data ? this.data.condicaoPrecificacao : null],
      restricoes: [this.data ? this.data.restricoes : null],
      historico: [this.data.historico ? this.data.historico : []],
      comissaoFornecedor: [this.data ? this.data.comissaoFornecedor : null],
      incentivoFornecedor: [this.data ? this.data.incentivoFornecedor : null],
      tourcode: [this.data ? this.data.tourcode : null],
      endosso: [this.data ? this.data.endosso : null],
      markups: [this.data ? this.data.markups : null],
      ativo: [this.data ? this.data.ativo : null],
      emissaoOnline: [this.data ? this.data?.emissaoOnline : null],
      tipoConfigPrecificacao: [this.data ? this.data.tipoConfigPrecificacao : null],
      ciasAereas: [this.data ? this.data.ciasAereas : null],
      tipoMarkup: [this.data ? this.data.tipoMarkup : null],
      editavel: [this.data ? this.data.editavel : null],
      statusPublicacao: [this.data ? this.data.statusPublicacao : null],
      idOrigem: [this.data ? this.data.idOrigem : null],
      idConfigEditavel: [this.data ? this.data.idConfigEditavel : null],
      forcarEmissao: [this.data ? this.data.forcarEmissao : false],
      zerarRepasse: [this.data ? this.data.zerarRepasse : false],
      nacInt: [this.data ? this.data.nacInt : null],
      id: [this.data ? this.data.id : null],
      versao: [this.data ? this.data.versao : null],
      osi: [this.data ? this.data.osi : null],
    });

    this.currentId = this.route.snapshot.params['id'];

    this.loading = false;
    this.initialized = true;

    if (!isNaN(this.currentId)) {
      this.loading = true;
      try {
        await this.loadItem(this.currentId);
      } finally {
        this.loading = false;
      }
    } else if (!isNaN(this.cloneId)) {
      this.loading = true;
      try {
        await this.loadClone(this.cloneId);
      } finally {
        this.loading = false;
      }
    }
  }

  protected async loadItem(id: number): Promise<void> {
    const response = await this.findById(id);
    await this.setValueItem(response);
  }

  protected async loadClone(id: any): Promise<void> {
    const response = await this.findCloneById(id);

    await this.setValueItem(response);
  }

  protected async setValueItem(data: any): Promise<void> {
    const {
      nome,
      empresas,
      prioridade,
      condicaoPrecificacao,
      restricoes,
      historico,
      comissaoFornecedor,
      incentivoFornecedor,
      tourcode,
      emissaoOnline,
      endosso,
      markups,
      ativo,
      tipoConfigPrecificacao,
      ciasAereas,
      tipoMarkup,
      editavel,
      statusPublicacao,
      idOrigem,
      idConfigEditavel,
      forcarEmissao,
      zerarRepasse,
      nacInt,
      id,
      versao,
      osi
    } = data.body;

    this.data = data.body;
    this.active = ativo;
    this.title = nome;
    this.pricingSettingsFormGroup.setValue({
      nome,
      empresas,
      prioridade,
      condicaoPrecificacao,
      restricoes,
      comissaoFornecedor: comissaoFornecedor * 100,
      incentivoFornecedor: incentivoFornecedor * 100,
      tourcode,
      endosso,
      markups,
      emissaoOnline,
      ativo,
      tipoConfigPrecificacao,
      ciasAereas,
      tipoMarkup,
      historico: historico ? historico : [],
      editavel,
      statusPublicacao,
      idOrigem,
      idConfigEditavel,
      forcarEmissao,
      zerarRepasse,
      nacInt,
      id,
      versao,
      osi
    });
    this.currentId = data.body.id;

    if(this.currentId) {
      const linkedCommercialPricingSettingsResponse = await this.loadLinkedCommercialPricingSettings(this.currentId);
      this.linkedCommercialPricingSettings = linkedCommercialPricingSettingsResponse.body;
    }

  }

  protected async loadLinkedCommercialPricingSettings(id) {
    return this.restangular.one(this.RESOURCE_NAME, id).getList('precificacoesComerciaisRelacionadas').toPromise();
  }

  public customEventButtonToRouter(event: MouseEvent, id: string): void{
    if (event.button > 1) {
      return;
    }

    let url = `redirect?route=commercialPricingSettings/${id}&token=${this.authService.getToken}`;


    if (event.button === 1 || event.ctrlKey) {
      window.open(url, '_blank');
    } else {
      window.location.href = url;
    }
  }

  protected async findCloneById(cloneId): Promise<void> {
    return this.restangular
      .one(this.RESOURCE_NAME, cloneId)
      .customGET('clonar')
      .toPromise();
  }

  public async clone() {
    this.loading = true;
    try {
      this.router.navigate(['pricingSettings' + '/' + 'criar'], {
        queryParams: { cloneId: this.currentId },
      });
      this.findCloneById(this.currentId).then((response) => {
        this.currentId = null;
        this.setValueItem(response);
      });
    } finally {
      this.loading = false;
    }
  }

  private findById(id: number): Promise<any> {
    return this.restangular.one(this.RESOURCE_NAME, id).get().toPromise();
  }

  public onSubmit() {
    this.loadingSearch = true;
    try {
      if (this.pricingSettingsFormGroup.controls.ativo.value == false ){
        this.loadingSearch = false;
        this.snackBar.openFromComponent(SnackBarComponent, {
          data: {
            title: 'Alerta',
            subTitle: 'Precificação Inativa,  Restaure antes para poder ser editada',
            buttons: [{label: "Fechar", action: "close", color: "warn"}]
          }
        });
        this.loadingSearch = false;
      } else{
        if (this.pricingSettingsFormGroup.valid) {
          let request: any;
          let value = this.pricingSettingsFormGroup.value;

          if (value.empresas[0][0] === 'T' ){
            value.empresas.shift();
          }
          value.ativo = true;
          value.markups = this.data.markups;
          value.comissaoFornecedor = value.comissaoFornecedor / 100;
          value.incentivoFornecedor = value.incentivoFornecedor / 100;
          this.insertRestrictions(value);
          if (this.currentId != null) {
            request = this.restangular
              .one(this.RESOURCE_NAME, this.currentId)
              .customPUT(value);
          } else {
            request = this.restangular.all(this.RESOURCE_NAME).post(value);
          }
          request.subscribe(
            (response) => {
              this.router.navigateByUrl('/pricingSettings');
            },
            (err) => {
              this.snackBar.openFromComponent(SnackBarComponent, {
                data: {
                  httpErrorResponse: err,
                  buttons: [
                    {
                      label: 'Mais informações',
                      action: 'information',
                      color: 'warn',
                    },
                    { label: 'Fechar', action: 'close', color: 'warn' },
                  ],
                },
              });
              this.loadingSearch = false;
            }
          );
        }else {
          this.loadingSearch = false;
          this.pricingSettingsFormGroup.markAllAsTouched();
          this.snackBar.openFromComponent(SnackBarComponent, {
            data: {
              title: 'Alerta',
              subTitle:
                'Verifique se todos os campos estão preenchidos corretamente',
              buttons: [{ label: 'Fechar', action: 'close', color: 'warn' }],
            },
          });
        }
      }
    } finally {
      this.loading = false;
    }
  }

  private insertRestrictions(value: any) {
    value.restricoes = [];
    value.restricoes = this.data.restricoes;
  }

  public showHistory(event: Event): void {
    event.preventDefault();
    if(this.isFeedHistEmpty) {
      this.data.historico.forEach((element, index) => {
        if(index === (this.data.historico.length - 1)){
          this.feedHist(element, (diff) => {
            element.diff = diff;
          });
        }else {
          this.feedHist(element, (diff) => {
            element.diff = diff;
          });
        }
      });

      this.isFeedHistEmpty = false;
    }

    const dialogRef = this.dialog.open(SimpleHistoryComponent, {
      autoFocus: false,
      maxHeight: '600px',
      width: '700px',
      data: {
        historico: this.data.historico,
        id: this.data.id,
        nome: this.data.nome,
        auditoria: this.data.auditoria
    },
    });
  }
  public restoreItemDialog(event: Event) {
    event.preventDefault();
    this.showConfirmationDialog(
      {
        title: 'Alerta',
        message: "Deseja restaurar o registro? ao ser restaurado o status da configuração será NÃO PUBLICADO",
        buttons: [
          { label: 'Não', action: 'close', color: 'cancel' },
          {
            label: 'Sim',
            action: (confirm) => {
              this.restoreItem();
              confirm();
            },
            color: 'save',
          },
        ],
        align: 'end',
      },
      () => this.restoreItem()
    );
  }

  public async restoreItem() {
    this.loading = true;
    try {
      await this.restangular
        .one(this.RESOURCE_NAME, this.currentId)
        .customPUT({}, 'ativar')
        .toPromise();
      await this.loadItem(this.currentId);
    } catch (err) {
      this.snackBar.openFromComponent(SnackBarComponent, {
        data: {
          httpErrorResponse: err,
          buttons: [
            { label: 'Mais informações', action: 'information', color: 'warn' },
            { label: 'Fechar', action: 'close', color: 'warn' },
          ],
        },
      });
    } finally {
      this.loading = false;
    }
  }

  public removeItem(event: Event) {
    event.preventDefault();
    this.showConfirmationDialog(
      {
        title: 'Alerta',
        message: 'Deseja inativar o registro?',
        buttons: [
          { label: 'Não', action: 'close', color: 'cancel' },
          {
            label: 'Sim',
            action: (confirm) => {
              this.deleteItem();
              confirm();
            },
            color: 'save',
          },
        ],
        align: 'end',
      },
      () => this.deleteItem()
    );
  }

  private async deleteItem() {
    this.loading = true;
    try {
      await this.restangular
        .one(this.RESOURCE_NAME, this.currentId)
        .remove()
        .toPromise();
      if (this.data.statusPublicacao === 'NAO_PUBLICADA') {
        await this.router.navigateByUrl("/pricingSettings");
      } else {
        await this.loadItem(this.currentId);
      }
    } finally {
      this.loading = false;
    }
  }

  private showConfirmationDialog(data: DialogData, action: Function): void {
    const dialogRef = this.dialog.open(DialogComponent, {
      maxWidth: '400px',
      data: data,
    });
  }

  public markupValue2Form({
    produto,
    markupBusca,
    markupMinimo,
    fee,
    zeraDu,
    zeraMkpFornecedor,
    zeraTaxaBolsaRC,
    moedaRepasse,
    taxaBolsa,
    taxaBolsaPerc,
    taxaBolsaCHD,
    taxaBolsaINF,
    taxaBolsaPercCHD,
    taxaBolsaPercINF,
    codigoRcSica,
    markupEmFatorDivisao
  }: IMarkupProduto) {
    return {
      produto,
      markupBusca: (markupBusca * 100).toFixed(4),
      markupMinimo: (markupMinimo * 100).toFixed(4),
      fee: fee * 100,
      zeraDu,
      zeraMkpFornecedor,
      zeraTaxaBolsaRC,
      moedaRepasse,
      taxaBolsa,
      taxaBolsaPerc: taxaBolsaPerc * 100,
      taxaBolsaCHD,
      taxaBolsaINF,
      taxaBolsaPercCHD: taxaBolsaPercCHD * 100,
      taxaBolsaPercINF: taxaBolsaPercINF * 100,
      codigoRcSica: codigoRcSica ? codigoRcSica : null,
      markupEmFatorDivisao: (+markupEmFatorDivisao).toFixed(6),
    };
  }

  public form2MarkupValue({
    produto,
    markupBusca,
    markupMinimo,
    fee,
    zeraDu,
    zeraMkpFornecedor,
    zeraTaxaBolsaRC,
    moedaRepasse,
    taxaBolsa,
    taxaBolsaPerc,
    taxaBolsaCHD,
    taxaBolsaINF,
    taxaBolsaPercCHD,
    taxaBolsaPercINF,
    codigoRcSica,
    markupEmFatorDivisao,
  }: IMarkupProduto) {
    function keepRcSica() {
      return (
        taxaBolsa > 0 ||
        taxaBolsaPerc > 0 ||
        taxaBolsaCHD > 0 ||
        taxaBolsaPercCHD > 0 ||
        taxaBolsaINF > 0 ||
        taxaBolsaPercINF > 0
      )
    }
    return {
      produto,
      markupBusca: (markupBusca / 100).toFixed(6),
      markupMinimo: markupMinimo / 100,
      fee: fee / 100,
      zeraDu,
      zeraMkpFornecedor,
      zeraTaxaBolsaRC,
      moedaRepasse,
      taxaBolsa,
      taxaBolsaPerc: taxaBolsaPerc / 100,
      taxaBolsaCHD,
      taxaBolsaINF,
      taxaBolsaPercCHD: taxaBolsaPercCHD / 100,
      taxaBolsaPercINF: taxaBolsaPercINF / 100,
      codigoRcSica: !keepRcSica() ? '' : codigoRcSica,
      markupEmFatorDivisao: markupEmFatorDivisao,
    };
  }

  public showSaveAndPublishDialog(event: Event) {
    event.preventDefault();
    this.loadingSearch = true;
    if (this.pricingSettingsFormGroup.controls.ativo.value == false ){
      this.snackBar.openFromComponent(SnackBarComponent, {
        data: {
          title: 'Alerta',
          subTitle: 'Precificação Inativa,  Restaure antes para poder ser editada',
          buttons: [{label: "Fechar", action: "close", color: "warn"}]
        }
      });
      this.loadingSearch = false;
    } else {
      this.showConfirmationDialog(
        {
          title: 'Salvar e Publicar Configuração',
          message: 'Tem certeza de que deseja salvar e publicar este item?',
          buttons: [
            { label: 'Cancelar', action: 'close', color: 'cancel' },
            {
              label: 'Sim',
              action: (confirm) => {
                this.saveAndPublish();
                confirm();
              },
              color: 'save',
            },
          ],
          align: 'end',
        },
        () => this.saveAndPublish()
      );
    }
  }

  public saveAndPublish() {
    this.loading = true;
    try {
      if (this.pricingSettingsFormGroup.valid) {
        let request: any;
        let value = this.pricingSettingsFormGroup.value;
        value.ativo = true;
        value.markups = this.data.markups;
        value.comissaoFornecedor = value.comissaoFornecedor / 100;
        value.incentivoFornecedor = value.incentivoFornecedor / 100;
        this.insertRestrictions(value);
        if (this.currentId != null) {
          request = this.restangular
            .one(this.RESOURCE_NAME, this.currentId)
            .customPUT(value);
        } else {
          request = this.restangular.all(this.RESOURCE_NAME).post(value);
        }
        request.subscribe(
          async (response) => {
            const { id } = response.body;
            await this.restangular
              .one(this.RESOURCE_NAME, id)
              .customPUT({}, 'publicar')
              .subscribe((response) => {
                this.router.navigateByUrl('/pricingSettings');
              });
          },
          (err) => {
            this.snackBar.openFromComponent(SnackBarComponent, {
              data: {
                httpErrorResponse: err,
                buttons: [
                  {
                    label: 'Mais informações',
                    action: 'information',
                    color: 'warn',
                  },
                  { label: 'Fechar', action: 'close', color: 'warn' },
                ],
              },
            });
            this.loadingSearch = false;
            this.loading = false;
          }
        );
      } else {
        this.loadingSearch = false;
        this.pricingSettingsFormGroup.markAllAsTouched();
        this.snackBar.openFromComponent(SnackBarComponent, {
          data: {
            title: 'Alerta',
            subTitle:
              'Verifique se todos os campos estão preenchidos corretamente',
            buttons: [{ label: 'Fechar', action: 'close', color: 'warn' }],
          },
        });
      }
    } finally {
      this.loading = false;
    }
  }

  public showCodigoRCSica() {
    return (
      this.taxaBolsaControl?.control?.value > 0 ||
      this.taxaBolsaPercControl?.control?.value > 0 ||
      this.taxaBolsaCHDControl?.control?.value > 0 ||
      this.taxaBolsaPercCHDControl?.control?.value > 0 ||
      this.taxaBolsaINFControl?.control?.value > 0 ||
      this.taxaBolsaPercINFControl?.control?.value > 0
    )
  }


  public async feedHist(hist, callback) {
    if (hist.configPrecificacao.diff === undefined) {
      await this.restangular.one(this.RESOURCE_NAME, hist.configPrecificacao.id).get()
        .subscribe(response => {
          let body = response.body;
          let historico = camposComparaveisConfiguracao(body);
          let atual = camposComparaveisConfiguracao(this.data);

          if(!historico.editavel && !historico.ativo && !atual.ativo){
            historico.ativo = true;
          }
          let diff = this.objectDiff.diff(historico, atual);
          hist.configPrecificacao.diff = diff;
          callback(this.objectDiff.toJsonDiffView(diff));
        });
    }

    function camposComparaveisConfiguracao(data) {

      return {
        nome: data.nome,
        ativo: data.ativo,
        editavel: data.editavel,
        prioridade: data.prioridade,
        condicaoPrecificacao: data.condicaoPrecificacao,
        comissaoFornecedor: data.comissaoFornecedor,
        incentivoFornecedor: data.incentivoFornecedor,
        tourcode: data.tourcode,
        endosso: data.endosso,
        forcarEmissao: data.forcarEmissao === 'true',
        zerarRepasse: data.zerarRepasse === 'true',
        mensagemTriagem: data.mensagemTriagem,
        ciasAereas: camposComparaveisCiasAereas(data.ciasAereas),
        markups: camposComparaveisMarkup(data.markups),
        restricoes: camposComparaveisRestricao(data.restricoes),
        osi: data.osi
      }
    }

    function camposComparaveisCiasAereas(ciasAereas) {
      return ciasAereas.map(ciaAerea => ({
        codigo: ciaAerea.codigo,
        nome: ciaAerea.nome,
        regraTaxaDu: ciaAerea.regraTaxaDu,
        nomeTratado: ciaAerea.nomeTratado,
      }))
    }

    function camposComparaveisMarkup(markups) {
      return markups.map(markup => ({
        produto: {nome: markup.produto.nome??'', descricao: markup.produto.descricao},
        markupBusca: markup.markupBusca,
        fee: markup.fee,
        comissaoCliente: markup.comissaoCliente,
        incentivoCliente: markup.incentivoCliente,
        repasseCliente: markup.repasseCliente,
        zeraDu: markup.zeraDu === 'true',
        taxaBolsa: markup.taxaBolsa,
        taxaBolsaCHD: markup.taxaBolsaCHD,
        taxaBolsaINF: markup.taxaBolsaINF,
        taxaBolsaPerc: markup.taxaBolsaPerc,
        taxaBolsaPercCHD: markup.taxaBolsaPercCHD,
        taxaBolsaPercINF: markup.taxaBolsaPercINF,
        markupEmFatorDivisao: markup.markupEmFatorDivisao,
      }))
    }

    function camposComparaveisRestricao(restricoes) {
      return restricoes.map(restricao =>
        ({
          tipoOperador: restricao.tipoOperador,
          tipoAgrupamento: restricao.tipoAgrupamento,
          tipoRestricao: restricao.tipoRestricao,
          valores: (restricao.valores)
            ? restricao.valores
              .sort((a, b) => (a.valor < b.valor) ? -1 : (a.valor > b.valor) ? 1 : 0)
              .map(valor => ({tipoRestricao: valor.tipoRestricao, valor: valor.valor}))
            : restricao.valores,
          restricoes: camposComparaveisRestricao(restricao.restricoes),
        })
      )
    }
  }

  factorChange(event: any) {
    if (this.notNullOrEmpty(event.target.value)) {
      const factor = (event.target.value / 1);
      const decimal = (1 / factor - 1);
      this.formSubregistry.detailForm.controls.markupBusca.setValue((decimal * 100).toFixed(6));
      this.formSubregistry.detailForm.controls.markupEmFatorDivisao.setValue(factor.toFixed(6));
      this.formSubregistry.detailForm.controls.markupBusca.setValue((decimal * 100).toFixed(6));
    }
  }

  decimalChange(event: any) {
    if (this.notNullOrEmpty(event.target.value)) {
      const decimal = (event.target.value / 100);
      const factor = 1 / (decimal + 1);
      this.formSubregistry.detailForm.controls.markupBusca.setValue((decimal * 100).toFixed(6));
      this.formSubregistry.detailForm.controls.markupEmFatorDivisao.setValue(factor.toFixed(6));
    }
  }

  private notNullOrEmpty(value: any) {
    return value !== null && value.toString().length > 0;
  }

  public getMarkupLiquidoEmPorcentagem(markupBusca: any): string {
    const comissaoFornecedorDecimal = this.pricingSettingsFormGroup.value.comissaoFornecedor;
    const incentivoFornecedorDecimal = this.pricingSettingsFormGroup.value.incentivoFornecedor/100;
    const markupLiquido = ((1 - comissaoFornecedorDecimal) * (1 - incentivoFornecedorDecimal) * (Number(markupBusca) + 1)) - 1;
    return `${(markupLiquido * 100).toFixed(6)} %`;
  }

  public showPriority(tipoConfigPrecificacao: string) : boolean {
    return CONFIGURACOES_COM_PRIORIDADE.some(c => c.includes(tipoConfigPrecificacao));
  }

  public isRetrita(tipoConfigPrecificacao: string) : boolean {
    return tipoConfigPrecificacao === 'RESTRITA';
  }

  public isRetritaOrSucessor(tipoConfigPrecificacao: string) : boolean {
    return tipoConfigPrecificacao === 'RESTRITA' || tipoConfigPrecificacao === 'SUCESSOR';
  }
}
