package br.tur.reservafacil.precificadorfrontapi.domain.repository;

import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoBusca;
import br.tur.reservafacil.dominio.tipo.SistEmis;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON> on 4/26/16.
 */
public interface ConfiguracaoBuscaRepository extends VersionavelRepository<ConfiguracaoBusca, Integer> {

    ConfiguracaoBusca findByPkEager(Integer id);

    List<ConfiguracaoBusca> findConfiguracaoSoComRestricaoByEmpresaCiaSisteEmisProduto(Empresa empresa, Collection<String> cias,
                                                                                       Collection<SistEmis> sistEmis, String produto,
                                                                                       Boolean isBuscaDeTeste);

    List<ConfiguracaoBusca> findByParameters(int page, int pageSize, String orderBy, Map<String, String> params);


    List<ConfiguracaoBusca> findConfiguracaoSoComRestricaoByEmpresa(Empresa empresa);

    Integer getCurrentVersionByIdOrigem(Integer idOrigem);

    Integer countByParameters(Map<String, String> params);

    List<ConfiguracaoBusca> buscaConfiguracoesDeBuscaParaNotificacao();

    List<ConfiguracaoBusca> findByKeys(Set<Integer> ids);

    List<ConfiguracaoBusca> findEagerByParameters(Map<String, String> params);

    List<ConfiguracaoBusca> findConfiguracaoBuscaPorGrupoEEmpresaESistEmisProduto(String grupo, String empresa, SistEmis sistEmis,
                                                                                  String produto);

    List<ConfiguracaoBusca> findConfiguracaoBuscaPorGrupoEEmpresaCiaProduto(String grupo, String empresa, String cia, String produto);

    List<ConfiguracaoBusca> findEagerByKeys(Set<Integer> idsConfiguracoesPrecificacao);

    /**
     * @param idOrigem o id da configuração de origem para encontrar a configuração editável
     * @return o id da configuração de busca editavel, através do id de uma configuração não editavel
     */
    Integer findIdConfiguracaoEditavelByIdOrigem(Integer idOrigem);
}
