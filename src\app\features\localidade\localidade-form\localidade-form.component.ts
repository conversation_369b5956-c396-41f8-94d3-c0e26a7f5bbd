import { Component } from "@angular/core";
import { FormGroup } from "@angular/forms";

@Component({
  selector: "app-localidade-form",
  templateUrl: "./localidade-form.component.html",
})
export class LocalidadeFormComponent {
  public title: string;

  public stateFilters = {};
  public cityFilters = {};

  formDefault = {
    codigoIata: [null],
    country: [null],
    state: [null],
    city: [null],
    ativo: [true],
  };

  constructor() {
    this.value2FormValue = this.value2FormValue.bind(this);
  }

  public value2FormValue = (responseFromApi: any): any => {
    if (!responseFromApi) {
      return {};
    }

    this.title = `${responseFromApi.codigoIata}`;

    if (responseFromApi.country) {
      this.stateFilters = { country: responseFromApi.country.id };
    }
    if (responseFromApi.state) {
      this.cityFilters = { state: responseFromApi.state.id };
    }

    return {
      codigoIata: responseFromApi.aeroporto,
      country: responseFromApi.country,
      state: responseFromApi.state,
      city: responseFromApi.codigoCidade ?? null,
      ativo: responseFromApi.ativo,
    };
  };

  public formValue2Value = (formValue: any): any => {
    return {
      codigoIata: formValue.codigoIata?.codigo,
      codigoPais: formValue.country?.codigo,
      codigoEstado: formValue.state?.codigo,
      codigoCidade: formValue.city ? String(formValue.city).trim() : null,
      ativo: formValue.ativo,
    };
  };

  public onCountryChange(selection: any, form: FormGroup): void {
    form.get("state").reset();
    form.get("city").reset();

    this.stateFilters = { country: selection?.id };
    this.cityFilters = {};
  }

  public onStateChange(selection: any, form: FormGroup): void {
    form.get("city").reset();

    this.cityFilters = { state: selection?.id };
  }
}
