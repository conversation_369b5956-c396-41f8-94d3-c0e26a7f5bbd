.list-settings-container((scroll)='onScroll($event)')
  div(fxLayout='column')
    div(*ngIf='loading' fxLayoutAlign='center' fxFlex)
      mat-progress-bar(mode='query')
    .topbar.custom-dark-theme(fxLayout='row' fxLayoutAlign='space-between center')
      div(fxLayoutGap='10px')
        button(*ngIf='showSearchButton' mat-stroked-button='', (click)='openFilters()')
          mat-icon search
        button(*ngIf='canAddNew' mat-stroked-button='', [routerLink]="'new'")
          mat-icon add
        button(*ngIf='showUploadButton' mat-stroked-button='', [routerLink]="'plain'")
          mat-icon upload_file
        button(mat-stroked-button, (click)='showPublishList()',*ngIf='publishFeature' ,matTooltip="Habilitar publicar ou cancelar em massa")
          mat-icon checklist_rtl
        button(mat-stroked-button='', (click)='selectAllItens();' matTooltip="Selecionar/Deselecionar Todos",*ngIf='publishFeature && showPublishFeature')
          mat-icon check_box
        button(mat-stroked-button, (click)='publishList()' matTooltip="Publicar selecionados",*ngIf='publishFeature && showPublishFeature')
          mat-icon send
        button(mat-stroked-button, (click)='cancelAllList()' matTooltip="Cancelar selecionados", *ngIf="showPublishFeature && showCancelAllList")
          mat-icon delete
        button(mat-stroked-button, (click)='toggleActiveStatusList()' matTooltip="Inativar/Ativar selecionados", *ngIf="showPublishFeature && showCancelAllList")
          mat-icon toggle_on
        button(*ngIf='exportxls' mat-stroked-button='', (click)='download()')
          mat-icon cloud_download
        button(*ngIf='importxls' mat-stroked-button='', (click)='openXlsImport()')
          mat-icon cloud_upload
        button(*ngIf='exportxlsPropertiesFirstButton.active' mat-stroked-button='', (click)='genericDownload(exportxlsPropertiesFirstButton)', [matTooltip]='exportxlsPropertiesFirstButton.matTooltip')
          mat-icon cloud_download
        button(*ngIf='exportxlsPropertiesSecondButton.active' mat-stroked-button='', (click)='genericDownload(exportxlsPropertiesSecondButton)', [matTooltip]='exportxlsPropertiesSecondButton.matTooltip')
          mat-icon download_for_offline
        button(*ngIf='showFileHistoryButton' mat-stroked-button='', [routerLink]="'fileHistory'", matTooltip="Histórico de arquivos")
          mat-icon history
      mat-chip-list(style='max-width: calc(100% - 800px);')
        mat-chip.filterChips(*ngFor='let filter of filterList', (click)='openFilters()')
          div(fxLayout='row' fxLayoutAlign='space-between center')
            span {{filter.title | titlecase}}: {{filter.value | underlineToSpace | titlecase}}
            button.filterChipDelBtn(mat-icon-button='', (click)='removeFilter($event, filter)')
              mat-icon.filterChipDelBtnIcon close
      div(fxLayoutGap='10px')
        mat-form-field.paginator-input-field(style='width: 70px;')
          mat-label Ir para
          input(matInput, type='number', #pageInput='', (keyup.enter)='goToPage(pageInput.value)')
        mat-paginator(#paginator='', [length]='length', [pageIndex]='pageIndex', [pageSize]='pageSize', [pageSizeOptions]='[12, 32, 100]', (page)='pageEvent = listData($event)' showfirstlastbuttons='true' style='background-color:transparent;display:inline-block;')
    .div(fxLayout='row' fxLayoutAlign='end center')
      div(style='margin-right: 12px; margin-top: 8px; margin-button: 10px;', fxLayoutGap='10px')
        button(*ngIf='genericSubmitProperties.active',  class='custom-button', mat-stroked-button='', (click)='genericSubmit(genericSubmitProperties)')
          | {{ genericSubmitProperties.buttonLabel }}
  dts-select-container(#container='dts-select-container', [(selectedItems)]='selectedItems', [custom]='true')
    div.listGridContainer
      mat-nav-list(fxLayout='column' fxLayoutAlign='space-around stretch')
        mat-list-item.listItemGrid(*ngFor='let item of dataSource')
          a(fxFlex [routerLink]='isClickable ? [item.id] : []')
            mat-card([dtsSelectItem]='item')
              a.nav-link
                div
                  div
                    mat-checkbox(
                      (change)="onCheckedChange(item,$event)",
                      (click)="clickHandler($event)",
                      *ngIf="showPublishFeature",
                      [checked]="this.itemsToPublish.includes(item.id)",
                      fxLayout='row' fxLayoutGap='10px' fxFlex fxFlexAlign='space-between center')
                  div(fxLayout='row' fxLayoutGap='10px' fxFlex fxFlexAlign='space-between center')
                    div(fxFlex)
                      ng-template(*ngTemplateOutlet='listTemplate; context: \{ item: item \}')
                    div(*ngIf='isActive(item) !== undefined' fxFlexAlign='center center' style='display: flex; align-items: center;')
                      mat-chip-list
                        mat-chip.activeChip.supscript {{ isActive(item) ? 'Ativo' : 'Inativo' }}
                    div(*ngIf="isPublished(item)" fxFlexAlign='center center' style='display: flex; align-items: center;' )
                      mat-icon([ngStyle]="{'color': (published) ? 'green': 'red'}" matTooltip="{{statusPublicacaoToolTip}}") {{statusPublicacaoIcon}}
                    div(*ngIf='showRemoveButton' fxFlexAlign='center center' style='display: flex; align-items: center;')
                      button(*ngIf='!hasActiveField' mat-icon-button (click)='excluirItem(item.id, $event)' matTooltip='Excluir' matTooltipPosition='above' style={ 'padding': '6px' })
                        mat-icon delete
                      button(*ngIf='isActive(item) && isActive(item) !== undefined' mat-icon-button (click)='excluirItem(item.id, $event)' matTooltip='Inativar configuração' matTooltipPosition='above')
                        mat-icon delete
                      button(*ngIf='!isActive(item) && isActive(item) !== undefined' mat-icon-button (click)='restaurarItem(item.id, $event)' matTooltip='Restaurar configuração' matTooltipPosition='above')
                        mat-icon restore_from_trash


