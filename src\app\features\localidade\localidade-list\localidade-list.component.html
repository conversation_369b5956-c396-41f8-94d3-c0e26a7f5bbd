<app-simple-list
  #simpleList
  apiRoute="localidades"
  name="localidades"
  [filterDialogComponent]="filterTemplate"
  [ativoByDefault]="false"
>
  <ng-template #listTemplate let-item="item">
    <div fxFlex style="padding: 20px">
      <span>{{ item.codigoIata }} - {{ item.codigoEstado }} - {{ item.codigoCidade }}</span
      ><br />
      <small>{{ item.codigoEstado }} - {{ item.codigoPais }}</small>
    </div>
  </ng-template>
</app-simple-list>

<ng-template #filterTemplate let-data>
  <app-simple-filter-dialog
    [data]="data"
    [dialogClose]="data.dialogClose"
    [formDefault]="{ codigoIata: '', codigoCidade: '' }"
  >
    <ng-template #filtersTemplate let-getFormControl="getFormControl">
      <div
        fxLayout="row"
        fxLayoutGap="15px"
        fxLayoutAlign="space-between center"
      >
        <mat-form-field appearance="standard" fxFlex="100">
          <mat-label>Iata</mat-label>
          <input
            matInput
            [formControl]="getFormControl('codigoIata')"
            placeholder="Iata do Aeroporto"
          />
        </mat-form-field>
        <mat-form-field appearance="standard" fxFlex="100">
          <mat-label>Cidade</mat-label>
          <input
            matInput
            [formControl]="getFormControl('codigoCidade')"
            placeholder="Nome da Cidade"
          />
        </mat-form-field>
      </div>
    </ng-template>
  </app-simple-filter-dialog>
</ng-template>
