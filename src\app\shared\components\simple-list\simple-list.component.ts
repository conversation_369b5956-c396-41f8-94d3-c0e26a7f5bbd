import { ComponentType } from "@angular/cdk/portal";
import {
  Component,
  ContentChild,
  Input,
  OnInit,
  TemplateRef,
  ViewChild,
} from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { MatPaginator, PageEvent } from "@angular/material/paginator";
import { MatSnackBar } from "@angular/material/snack-bar";
import { StorageMap } from "@ngx-pwa/local-storage";
import { SelectContainerComponent } from "ngx-drag-to-select";
import { Restangular } from "ngx-restangular";
import { PermissionService } from "src/app/core/services/permission.service";
import { MediaType } from "src/app/core/utils/media-type";
import { DialogComponent } from "../dialog/dialog.component";
import { DialogData } from "../dialog/DialogData";
import { FileService } from "../services/file.service";
import { SnackBarComponent } from "../snack-bar/snack-bar.component";
import { Router } from "@angular/router";

@Component({
  selector: "app-simple-list",
  templateUrl: "./simple-list.component.pug",
  styleUrls: ["./simple-list.component.scss"],
})
export class SimpleListComponent implements OnInit {
  public readonly SELECTIONMODE_CLICK = "click";
  private apiRouteReport = "relatorio";
  published: boolean = false;
  statusPublicacaoToolTip: string = "";
  statusPublicacaoIcon: string = "verified";
  showPublishFeature: boolean = false;
  showCancelAllList: boolean = false;

  clickHandler(event) {
    if (this.showPublishFeature) {
      event.stopPropagation();
    }
  }

  public getInitialFilter() {
    if (
      this.name === "passenger-front" ||
      this.name === "registration-continente"
    ) {
      return { ...this.initialFilter };
    }
    if (!this.publishFeature && this.ativoByDefault) {
      const keyName = this.name === "user-front" ? "active" : "ativo";
      return {
        [keyName]: {
          field_display: "Ativo",
          field_title: "Status",
          field_value: "true",
          id: "true",
          nome: "Ativo",
        },
        ...this.initialFilter,
      };
    } else {
      return { ...this.initialFilter };
    }
  }

  loading = true;
  selectionMode = this.SELECTIONMODE_CLICK;

  pageEvent: PageEvent;
  dataSource: any[] = [];
  pageIndex = 0;
  pageSize = 12;
  length: number;

  protected filters: any = {};
  protected globalFilters: any = {};
  public filterList: any[] = [];
  public selectedItems: any[] = [];
  public itemsToPublish: any[] = [];
  public itemsToCancel: any[] = [];

  @Input() initialFilter: any = {};
  @Input() apiRoute: string;
  @Input() name: string;
  @Input() filterDialogComponent: ComponentType<unknown> | TemplateRef<any>;
  @Input() restoreFilters: boolean = true;
  @Input() profileReceived: (profile: any, globalFilters: any) => any;
  @Input() exportxls: boolean = false;
  @Input() importxls: boolean = false;
  @Input() importxlsDialogComponent: ComponentType<unknown> | TemplateRef<any>;
  @Input() titlexls: string = "Relatorio";
  @Input() canAddNew: boolean = true;
  @Input() showRemoveButton: boolean = true;
  @Input() isClickable: boolean = true;
  @Input() publishFeature: boolean = false;
  @Input() queryParams: any = {};
  @Input() ativoByDefault: boolean = true;
  @Input() showUploadButton: boolean = false;
  @Input() hasActiveField: boolean = true;
  @Input() exportxlsPropertiesFirstButton: any = {};
  @Input() exportxlsPropertiesSecondButton: any = {};
  @Input() genericSubmitProperties: any = {};
  @Input() showSearchButton: boolean = true;
  @Input() showFileHistoryButton: boolean = false;

  @ViewChild(SelectContainerComponent)
  private dtsSelector: SelectContainerComponent;
  @ViewChild('paginator') paginator: MatPaginator;

  @ContentChild("listTemplate")
  public listTemplate: TemplateRef<any>;

  constructor(
    private restangular: Restangular,
    public dialog: MatDialog,
    private storage: StorageMap,
    private permissionService: PermissionService,
    private fileService: FileService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.filters = this.getInitialFilter();

    this.permissionService.getAccessProfile.subscribe(async (profile: any) => {
      // TODO - Verificar se o filtro global de empresa deve ser aplicado - se for aplicado criar estratégia para mergear os
      // filtros de telas que utilizam o filtro empresa com o filtro global;
      //this.globalFilters = this.applyEnterpriseGlobalFilter(profile);
      if (
        this.profileReceived !== null &&
        typeof this.profileReceived != "undefined"
      ) {
        this.globalFilters = this.profileReceived(profile, this.globalFilters);
      }

      if (this.restoreFilters) {
        const filters = await this.recoverFilters();
        this.filters = { ...this.filters, ...filters };
      }
      this.updateFilterDisplay();
      this.listData(null);
    });
  }

  protected applyEnterpriseGlobalFilter(profile: any): any {
    if (
      profile[0] !== null &&
      typeof profile[0] !== "undefined" &&
      profile[0].hasOwnProperty("empresas")
    ) {
      profile[0].empresas.forEach(
        (enterprise: any) => (enterprise.toString = () => enterprise.nome)
      );
      return { empresa: profile[0].empresas };
    }
  }

  public listData(event?: PageEvent): PageEvent {
    this.dataSource = [];
    this.loading = true;

    if (typeof event !== "undefined" && event !== null) {
      this.pageIndex = event.pageIndex;
      this.pageSize = event.pageSize;
    }

    const pagination = {
      pageNumber: this.pageIndex + 1,
      pageLimit: this.pageSize,
    };

    this.restangular
      .all(this.apiRoute)
      .getList({
        ...this.queryParams,
        ...this.parseFilters(this.filters),
        ...pagination,
        ...this.parseFilters(this.globalFilters),
      })
      .subscribe((response) => {
        this.dataSource = response.data.plain();
        if (response.headers.get("total-itens") !== null) {
          this.dataSource = response.data.plain();
          this.length = response.headers.get("total-itens");
        } else if (response.headers.get("Total-itens") !== null) {
          this.dataSource = response.data.plain();
          this.length = response.headers.get("Total-itens");
        } else {
          this.length = response.headers.get("pages") * this.pageSize;
        }
        this.loading = false;
      });

    return event;
  }

  public goToPage(page: string): void {
    const pageNumber = Number(page);
    if (!isNaN(pageNumber) && pageNumber > 0 && pageNumber <= this.paginator.getNumberOfPages()) {
      const newPageIndex = pageNumber - 1;

      this.paginator.pageIndex = newPageIndex;

      const pageEvent: PageEvent = {
        pageIndex: newPageIndex,
        pageSize: this.pageSize,
        length: this.length
      };

      this.listData(pageEvent);
    }
  }

  public download(): void {
    const filters = this.parseFilters(this.filters);
    this.loading = true;
    this.restangular
      .one(this.apiRoute)
      .withHttpConfig({ responseType: "arraybuffer" })
      .customGET(this.apiRouteReport, filters, null, {
        Accept: MediaType.OCTET_STREAM,
      })
      .subscribe(
        (res) => {
          this.fileService.download(res, this.titlexls);
          this.loading = false;
        },
        (err) => {
          this.loading = false;
        }
      );
  }

  public genericDownload(exportxlsProperties: any): void {
    const filters = this.parseFilters(this.filters);
    this.loading = true;
    this.restangular
      .one(this.apiRoute)
      .withHttpConfig({ responseType: "arraybuffer" })
      .customGET(exportxlsProperties.apiRouteReport, filters, null, {
        Accept: MediaType.OCTET_STREAM,
      })
      .subscribe(
        (res) => {
          this.fileService.download(res, exportxlsProperties.titlexls);
          this.loading = false;
        },
        (err) => {
          this.loading = false;
        }
      );
  }

  public isActive(item: any): boolean {
    if (typeof item.ativo !== "undefined") {
      return item.ativo;
    } else if (typeof item.active !== "undefined") {
      return item.active;
    } else {
      return item.ativo;
    }
  }

  public isPublished(item: any) {
    if (item.statusPublicacao !== undefined) {
      if (item.ativo === true || item.statusPublicacao) {
        switch (item.statusPublicacao) {
          case "PUBLICADA":
            this.statusPublicacaoIcon = "verified";
            this.published = true;
            this.statusPublicacaoToolTip = "Publicada";
            break;
          case "NAO_PUBLICADA":
            this.statusPublicacaoToolTip = "Não Publicada";
            this.statusPublicacaoIcon = "unpublished";
            this.published = false;
            break;
          case "INATIVO":
            this.statusPublicacaoToolTip = "Inativo";
            this.statusPublicacaoIcon = "disabled_by_default";
            this.published = false;
            break;
          case "EM_HISTORICO":
            this.statusPublicacaoToolTip = "Historico";
            this.statusPublicacaoIcon = "history";
            this.published = false;
            break;
        }
        return true;
      }
    }
    return false;
  }

  public onScroll(event?: any): void {
    this.dtsSelector.update();
  }

  public openXlsImport(): void {
    function dialogClose(result?: any) {
      dialogRef.close(result);
    }

    const dialogRef = this.dialog.open(this.importxlsDialogComponent, {
      data: { dialogClose: dialogClose },
    });
  }

  public showPublishList(): void {
    this.showPublishFeature = !this.showPublishFeature;
    this.isClickable = !this.isClickable;

    if (this.name === "searchSettings" || this.name === "pricingSettings") {
      this.showCancelAllList = true;
      this.isClickable = !this.isClickable;
    } else {
      this.showCancelAllList = false;
    }
  }

  onCheckedChange(item: any, event?: any) {
    if (this.itemsToPublish.includes(item.id)) {
      this.itemsToPublish.splice(this.itemsToPublish.indexOf(item.id, 0), 1);
    } else {
      this.itemsToPublish.push(item.id);
    }
    if (this.itemsToCancel.includes(item.id)) {
      this.itemsToCancel.splice(this.itemsToCancel.indexOf(item.id, 0), 1);
    } else {
      this.itemsToCancel.push(item.id);
    }
  }

  selectAllItens() {
    if (this.itemsToPublish.length > 0) {
      this.itemsToPublish = [];
    } else {
      this.itemsToPublish = this.dataSource.map((item) => {
        return item.id;
      });
    }
    if (this.itemsToCancel.length > 0) {
      this.itemsToCancel = [];
    } else {
      this.itemsToCancel = this.dataSource.map((item) => {
        return item.id;
      });
    }
  }

  async publishList() {
    const ids = { ids: this.itemsToPublish };
    await this.restangular
      .one(this.apiRoute)
      .customPOST(ids, "publicar")
      .subscribe((response) => {
        window.location.reload();
      });
  }

  async cancelAllList(): Promise<void> {
    if (this.itemsToCancel.length === 0) {
      return;
    }
    const confirmationDialogData: DialogData = {
      title: "Alerta",
      message: "Deseja cancelar os registros selecionados?",
      buttons: [
        { label: "Não", action: "close", color: "cancel" },
        {
          label: "Sim",
          action: (confirm) => {
            this.performCancelAll();
            confirm();
          },
          color: "save",
        },
      ],
      align: "center",
    };

    this.showConfirmationDialog(confirmationDialogData, () => {});
  }

  async toggleActiveStatusList(): Promise<void> {
    if (this.itemsToCancel.length === 0) {
      return;
    }
    const confirmationDialogData: DialogData = {
      title: "Alerta",
      message: "Deseja inverter o status ativo/inativo dos registros selecionados?",
      buttons: [
        { label: "Não", action: "close", color: "cancel" },
        {
          label: "Sim",
          action: (confirm) => {
            this.performToggleActiveStatus();
            confirm();
          },
          color: "save",
        },
      ],
      align: "center",
    };

    this.showConfirmationDialog(confirmationDialogData, () => {});
  }

  private performCancelAll(): void {
    const ids = this.itemsToCancel;
    this.loading = true;
    this.restangular
      .all(this.apiRoute)
      .customPOST(ids, "cancelAll")
      .subscribe(
        () => {
          this.listData();
          window.location.reload();
        },
        (err) => {
          console.log(err);
          this.snackBar.openFromComponent(SnackBarComponent, {
            data: {
              httpErrorResponse: err,
              buttons: [
                {
                  label: "Mais informações",
                  action: "information",
                  color: "warn",
                },
                { label: "Fechar", action: "close", color: "warn" },
              ],
            },
          });
          this.loading = false;
        }
      );
  }

  private performToggleActiveStatus(): void {
    const ids = this.itemsToCancel;
    this.loading = true;
    this.restangular
      .all(this.apiRoute)
      .customPOST(ids, "toggleActiveStatus")
      .subscribe(
        () => {
          this.listData();
          window.location.reload();
        },
        (err) => {
          console.log(err);
          this.snackBar.openFromComponent(SnackBarComponent, {
            data: {
              httpErrorResponse: err,
              buttons: [
                {
                  label: "Mais informações",
                  action: "information",
                  color: "warn",
                },
                { label: "Fechar", action: "close", color: "warn" },
              ],
            },
          });
          this.loading = false;
        }
      );
  }

  public openFilters(): void {
    function dialogClose(result?: any) {
      dialogRef.close(result);
    }

    const dialogRef = this.dialog.open(this.filterDialogComponent, {
      height: "380px",
      width: "600px",
      data: { dialogClose: dialogClose, ...this.filters },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (typeof result !== "undefined") {
        if (result?.dialogClose !== undefined) {
          delete result.dialogClose;
        }
        this.filters = result;
        if (this.filters) {
          for (const member in this.filters) {
            if (this.filters[member] === null) {
              delete this.filters[member];
            }
          }
        } else {
          this.filters = {};
        }
        this.storeFilters(this.filters);
        this.updateFilterDisplay();
        this.listData();
      }
    });
  }

  public removeFilter(event: Event, filter: any): void {
    if (
      (filter.name !== "ativo" && filter.name !== "active") ||
      !this.ativoByDefault ||
      this.publishFeature
    ) {
      event.stopPropagation();
      delete this.filters[filter.name];
    }
    this.storeFilters(this.filters);
    this.updateFilterDisplay();
    this.listData();
  }

  private updateFilterDisplay() {
    const allFilters = { ...this.filters, ...this.globalFilters };
    this.filterList = [];

    for (const member in allFilters) {
      if (allFilters.hasOwnProperty(member)) {
        const current = Array.isArray(allFilters[member])
          ? allFilters[member]
          : [allFilters[member]];
        const title: string = current.map((c: any) =>
          c.hasOwnProperty("field_title") ? c.field_title : member.toString()
        )[0];
        const values: string[] = current.map((c: any) => {
          if (c.hasOwnProperty("field_display")) {
            return c.field_display;
          } else if (c.hasOwnProperty("field_value")) {
            return c.field_value;
          } else {
            return c.toString();
          }
        });
        values.forEach((value) =>
          this.filterList.push({ name: member, title, value })
        );
      }
    }
  }

  private storeFilters(load: any) {
    return this.storage
      .set(`${this.name}-filter`, JSON.stringify(load))
      .subscribe(() => {});
  }

  private async recoverFilters(): Promise<any> {
    const resp = await this.storage.get(`${this.name}-filter`).toPromise();

    if (typeof resp === "string" && resp.length > 0) {
      return JSON.parse(resp);
    }

    return {};
  }

  private parseFilters(filterObj: any) {
    const parsed: any = {};
    for (const member in filterObj) {
      if (filterObj[member] != null) {
        const memberValue = Array.isArray(filterObj[member])
          ? filterObj[member]
          : [filterObj[member]];
        parsed[member] = memberValue
          .map((item: any) => {
            if (item.hasOwnProperty("field_value")) {
              return item.field_title === "Empresa" &&
                this.apiRoute === "configuracaofee"
                ? this.formatEmpresaFeeValue(item.field_value)
                : item.field_value.toString();
            } else {
              return item.toString();
            }
          })
          .join(",");
      }
    }
    return parsed;
  }

  public excluirItem(itemId: number, event: Event): void {
    event.stopPropagation();
    event.preventDefault();
    this.showConfirmationDialog(
      {
        title: "Alerta",
        message: "Deseja inativar o registro?",
        buttons: [
          { label: "Não", action: "close", color: "cancel" },
          {
            label: "Sim",
            action: (confirm) => {
              this.deleteItem(itemId);
              confirm();
            },
            color: "save",
          },
        ],
        align: "center",
      },
      () => this.deleteItem(itemId)
    );
  }

  private deleteItem(itemId: any): void {
    this.loading = true;
    this.restangular
      .one(this.apiRoute, itemId)
      .remove()
      .subscribe(
        () => {
          this.listData();
        },
        (err) => {
          console.log(err);
          this.snackBar.openFromComponent(SnackBarComponent, {
            data: {
              httpErrorResponse: err,
              buttons: [
                {
                  label: "Mais informações",
                  action: "information",
                  color: "warn",
                },
                { label: "Fechar", action: "close", color: "warn" },
              ],
            },
          });
          this.loading = false;
        }
      );
  }

  public restaurarItem(itemId: number, event: Event): void {
    this.loading = true;
    event.stopPropagation();
    event.preventDefault();
    this.restangular
      .one(this.apiRoute, itemId)
      .customPUT({}, "ativar")
      .subscribe(
        () => {
          this.listData();
        },
        (err) => {
          console.log(err);
          this.snackBar.openFromComponent(SnackBarComponent, {
            data: {
              httpErrorResponse: err,
              buttons: [
                {
                  label: "Mais informações",
                  action: "information",
                  color: "warn",
                },
                { label: "Fechar", action: "close", color: "warn" },
              ],
            },
          });
          this.loading = false;
        }
      );
  }

  private showConfirmationDialog(data: DialogData, action: Function): void {
    const dialogRef = this.dialog.open(DialogComponent, {
      maxWidth: "400px",
      data: {
        title: data.title,
        message: data.message,
        buttons: data.buttons,
        align: data.align,
      },
    });
  }

  private formatEmpresaFeeValue(value: string): string {
    if (value) {
      let valueFormatted = value.replace(" - ", "-");
      return valueFormatted;
    }
  }

  async genericSubmit(genericSubmitProperties: any) {
    this.loading = true;
    const route = this.restangular.one(genericSubmitProperties.apiRoute);
    const body = this.dataSource;
    const method = genericSubmitProperties.httpMethod?.toUpperCase();

    if (method === "POST") {
      route.customPOST(body).subscribe((response) => {
        window.location.reload();
      });
    }

    if (method === "PUT") {
      route.customPUT(body).subscribe((response) => {
        window.location.reload();
      });
    }
  }
}
