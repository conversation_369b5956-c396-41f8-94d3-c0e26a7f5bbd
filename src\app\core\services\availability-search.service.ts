import {Injectable} from '@angular/core';
import {BehaviorSubject} from 'rxjs';
import {Restangular} from 'ngx-restangular';
import { SnackBarComponent } from "src/app/shared/components/snack-bar/snack-bar.component";
import { MatSnackBar } from "@angular/material/snack-bar";
@Injectable({
  providedIn: 'root'
})
export class AvailabilitySearchService {

  private _request: any;
  private _response = new BehaviorSubject<any>({});
  private _xmls = new BehaviorSubject<boolean>(false);
  private _tipoBusca = new BehaviorSubject<string>("");

  xmls = this._xmls.asObservable();
  tipoBusca = this._tipoBusca.asObservable();
  response = this._response.asObservable();

  RESOURCE_NAME = 'buscaDisponibilidade';
  readonly LOCAL_STORAGE_LAST_SEARCH = 'lastSearch';
  readonly LOCAL_STORAGE_LAST_SEARCH_ID = 'lastIdSearchs';
  readonly LOCAL_STORAGE_HAS_ID_CONFIG_BUSCA_OR_XMLS = 'hasIdConfigBuscaOrXmls';

  constructor(private _restangular: Restangular, private snackBar: MatSnackBar) {
  }

  public loadFilterById(id): Promise<void> {
    return this._restangular.one(this.RESOURCE_NAME).customGET('filtroBusca', {idFiltro: id}).toPromise();
  }

  availabilitySearch(request: any, xmls: boolean) {
    this._request = request;
    request.xmls = xmls;
    this._xmls.next(request.xmls);
    this._tipoBusca.next(request.tipoBusca);
    this._response.next({});
    this._restangular.all(this.RESOURCE_NAME).post(request).toPromise().then(value => {
      if (
        value?.data?.dadosBusca &&
        (value?.data?.idBusca || value?.data?.dadosBusca?.idConfigBusca)
      ) {
        const idBusca = value.data.idBusca ? value.data.idBusca : value.data.dadosBusca.idConfigBusca;
        this.loadFilterById(idBusca).then(responseId => {
          this.storeLastLocalSearch(idBusca, responseId);
        });
      }
      this._response.next(value);
    }).catch(err => {
      this._response.next(err);
      this.snackBar.openFromComponent(SnackBarComponent, {
        data: {
            httpErrorResponse: err,
            buttons:[
              {label: "Fechar", action: "close" , color: "warn"}]
          }
        });
    });
  }

  searchConfig(request): Promise<any> {
    this._request = request;
    return this._restangular.all(this.RESOURCE_NAME + '/configuracoesBusca').post(request).toPromise();
  }

  public getRequestDetails() {
    return this._request || {};
  }

  filterAvailability(request) {
    const {
      ciasAereas,
      sistEmis,
      fareType,
      flightNumber,
      fareFamily,
      vooDireto,
      vooExcluido
    } = request;
    this._request.ciasAereas = ciasAereas ? [ciasAereas] : [];
    this._request.sistEmis = sistEmis;
    this._request.fareType = fareType;
    this._request.flightNumber = flightNumber;
    this._request.fareFamily = fareFamily;
    this._request.sistEmis = sistEmis || 'TODOS';
    this._request.vooDireto = vooDireto === 'true';
    this._request.vooExcluido = vooExcluido !== 'false';

    this.cacheAvailability(['', this._request.sistEmis], null);
  }

  cacheAvailability(nome: any, page: any) {
    const request = this._request;
    request.idGwa = nome[0];
    request.sistEmis = nome[1] ? nome[1]: this._request.sistEmis;
    
    if (page) {
      const queryParams = {pageNumber: page};
      this._restangular.one(this.RESOURCE_NAME + '/cache').customPOST(request, null, queryParams, null).toPromise().then(value => {
          this._response.next(value);
        }
      );
    } else {
      this._restangular.all(this.RESOURCE_NAME + '/cache').post(request).toPromise().then(value => {
          this._response.next(value);
        }
      );
    }
  }

  excludedPricing(correlationId, hashVoos) {
    const queryParams = {correlationId, hashVoos};
    return this._restangular.one('voosExcluidos/precificacoesExcluidas').customGET(null, null, queryParams, null).toPromise().then (resp => {
      this._response.next(resp);
    }).catch(err => {
      this._response.next(err);
      throw err.message;
    });
  }

  debugPaymentCondition(recomendacao) {
    const request = {
      filtroDisponibilidade:
        {
          cabine: this._request.cabine,
          cache: false,
          ciasAereas: [],
          filial: this._request.filial,
          agencia: this._request.agencia,
          grupo: this._request.grupo,
          numAdt: this._request.numAdt,
          numChd: this._request.numChd,
          numInf: this._request.numInf,
          offerId: recomendacao.offerId,
          produtos: this._request.produtos,
          tipoBusca: this._request.tipoBusca,
          trechos: [],
          usuario: this._request.usuario,
          vooDireto: false,
          vooExcluido: false,
          xmls: false,
        },
      precificavelFiltro: {
        minimoVooHashList: recomendacao.voos.map(value => {
            return {
              aeroportoDestino: value.aeroportoDestino,
              aeroportoOrigem: value.aeroportoOrigem,
              dataDestino: value.dataDestino,
              dataOrigem: value.dataOrigem,
              numero: value.numero,
              classe: value.classe,
              sistEmis: recomendacao.sistEmis,
              baseTarifaria: value.baseTarifaria,
              codigoContrato: recomendacao.codigoContrato,
              officeIdReserva: recomendacao.officeIdReserva,
              officeIdEmissao: recomendacao.officeIdEmissao,
              credencialId: recomendacao.credencialId
              };
          }
        ),
      },
      precificavelHash: recomendacao.hashVoos
    };
    return this._restangular.all(this.RESOURCE_NAME + '/testaCondicaoPagamento').post(request).toPromise();
  }

  debugTariff(recomendacao) {
    const request = {
      filtroDisponibilidade:
        {
          cabine: this._request.cabine,
          cache: false,
          ciasAereas: [],
          filial: this._request.filial,
          agencia: this._request.agencia,
          grupo: this._request.grupo,
          numAdt: this._request.numAdt,
          numChd: this._request.numChd,
          numInf: this._request.numInf,
          offerId: recomendacao.offerId,
          produtos: this._request.produtos,
          tipoBusca: this._request.tipoBusca,
          trechos: [],
          usuario: this._request.usuario,
          vooDireto: false,
          vooExcluido: false,
          xmls: false,
        },
      precificavelFiltro: {
        minimoVooHashList: recomendacao.voos.map(value => {
            return {
              aeroportoDestino: value.aeroportoDestino,
              aeroportoOrigem: value.aeroportoOrigem,
              dataDestino: value.dataDestino,
              dataOrigem: value.dataOrigem,
              numero: value.numero,
              classe: value.classe,
              sistEmis: recomendacao.sistEmis,
              baseTarifaria: value.baseTarifaria,
              codigoContrato: recomendacao.codigoContrato,
              officeIdReserva: recomendacao.officeIdReserva,
              officeIdEmissao: recomendacao.officeIdEmissao,
              credencialId: recomendacao.credencialId
              };
          }
        ),
      },
      precificavelHash: recomendacao.hashVoos
    };
    return this._restangular.all(this.RESOURCE_NAME + '/testaPerfilTarifario').post(request).toPromise();
  }

  debugComercialPrincing(recomendacao) {
    const request = {
      filtroDisponibilidade:
        {
          cabine: this._request.cabine,
          cache: false,
          ciasAereas: [],
          filial: this._request.filial,
          agencia: this._request.agencia,
          grupo: this._request.grupo,
          numAdt: this._request.numAdt,
          numChd: this._request.numChd,
          numInf: this._request.numInf,
          offerId: recomendacao.offerId,
          produtos: this._request.produtos,
          tipoBusca: this._request.tipoBusca,
          trechos: [],
          usuario: this._request.usuario,
          vooDireto: false,
          vooExcluido: false,
          xmls: false,
        },
      precificavelFiltro: {
        minimoVooHashList: recomendacao.voos.map(value => {
            return {
              aeroportoDestino: value.aeroportoDestino,
              aeroportoOrigem: value.aeroportoOrigem,
              dataDestino: value.dataDestino,
              dataOrigem: value.dataOrigem,
              numero: value.numero,
              classe: value.classe,
              sistEmis: recomendacao.sistEmis,
              baseTarifaria: value.baseTarifaria,
              codigoContrato: recomendacao.codigoContrato,
              officeIdReserva: recomendacao.officeIdReserva,
              officeIdEmissao: recomendacao.officeIdEmissao,
              credencialId: recomendacao.credencialId
            };
          }
        ),
      },
      precificavelHash: recomendacao.hashVoos
    };
    return this._restangular.all(this.RESOURCE_NAME + '/testaPrecificacaoComercial').post(request).toPromise();
  }

  debugConfig(recomendacao) {
    const request = {
      filtroDisponibilidade:
        {
          cabine: this._request.cabine,
          cache: false,
          ciasAereas: [],
          filial: this._request.filial,
          agencia: this._request.agencia,
          grupo: this._request.grupo,
          numAdt: this._request.numAdt,
          numChd: this._request.numChd,
          numInf: this._request.numInf,
          offerId: recomendacao.offerId,
          produtos: this._request.produtos,
          tipoBusca: this._request.tipoBusca,
          trechos: [],
          usuario: this._request.usuario,
          vooDireto: false,
          vooExcluido: false,
          xmls: false,
        },
      precificavelFiltro: {
        minimoVooHashList: recomendacao.voos.map(value => {
            return {
              aeroportoDestino: value.aeroportoDestino,
              aeroportoOrigem: value.aeroportoOrigem,
              dataDestino: value.dataDestino,
              dataOrigem: value.dataOrigem,
              numero: value.numero,
              classe: value.classe,
              sistEmis: recomendacao.sistEmis,
              baseTarifaria: value.baseTarifaria,
              codigoContrato: recomendacao.codigoContrato,
              officeIdReserva: recomendacao.officeIdReserva,
              officeIdEmissao: recomendacao.officeIdEmissao,
              credencialId: recomendacao.credencialId
            };
          }
        ),
      },
      precificavelHash: recomendacao.hashVoos
    };
    return this._restangular.all(this.RESOURCE_NAME + '/testaConfiguracaoPrecificacao').withHttpConfig({timeout: 100}).post(request).toPromise();
    // .then(resp => {
    //   this._response.next(resp);
    // }).catch(err => {
    //   this._response.next(err);
    //   throw err.message;
    // });
  }

  async debugPrice(rateTokens, userName, userPassword, branchId, agencyId, groupId) {
    const request = {
      rateToken: rateTokens,
      user: userName,
      password: userPassword,
      branchId: branchId,
      agencyId: agencyId,
      groupId: groupId
    };
    return this._restangular.all(this.RESOURCE_NAME + '/testaTarifacao').post(request).toPromise();
}

  public storeLastLocalSearch(id: string, data: any) {
    this.setLastSearch(id, data);
  }

  private setLastSearch(id: string, data: string) {
    localStorage.setItem(this.LOCAL_STORAGE_LAST_SEARCH_ID, id);
    localStorage.setItem(this.LOCAL_STORAGE_LAST_SEARCH, JSON.stringify(data));
  }

  public setLastSearchId(id: string) {
    localStorage.setItem(this.LOCAL_STORAGE_LAST_SEARCH_ID, id);
  }

  public setHasIdConfigBuscaOrXmls(hasIdConfigBuscaOrXmls: boolean) : void {
    localStorage.setItem(this.LOCAL_STORAGE_HAS_ID_CONFIG_BUSCA_OR_XMLS, JSON.stringify(hasIdConfigBuscaOrXmls));
  }

  public getHasIdConfigBuscaOrXmls() : boolean {
    return JSON.parse(localStorage.getItem(this.LOCAL_STORAGE_HAS_ID_CONFIG_BUSCA_OR_XMLS));
  }


  public get getLastSearch() {
    return JSON.parse(localStorage.getItem(this.LOCAL_STORAGE_LAST_SEARCH));
  }

  public get getLastSearchedIds() {
    return localStorage.getItem(this.LOCAL_STORAGE_LAST_SEARCH_ID);
  }

  public destroy() {
    if (this._response) {
      this._response.unsubscribe();
    }
    if (this._xmls) {
      this._xmls.unsubscribe();
    }
  }

  public async refreshEmpresa(usuario: any): Promise<any> {
    const response = await this.getEmpresaById(usuario?.empresa?.id);
    return response?.body;
  }
  
  private async getEmpresaById(id: number) : Promise<any> {
    return this._restangular.one('empresas', id).get().toPromise();
  }



}
