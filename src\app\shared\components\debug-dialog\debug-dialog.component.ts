import { Component, Inject, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { RedirectService } from 'src/app/core/services/redirect.service';
import { Clipboard } from '@angular/cdk/clipboard';
import { MatSnackBar } from '@angular/material/snack-bar';
import * as _ from 'lodash';

@Component({
  selector: 'app-debug-dialog',
  templateUrl: './debug-dialog.component.html',
  styleUrls: ['./debug-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush, //evita re-render em massa
})
export class DebugDialogComponent implements OnInit {
  debugDialogForm: FormGroup;

  public originalData: any;
  public viewData: any;

  constructor(
    public dialogRef: MatDialogRef<DebugDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public formBuilder: FormBuilder,
    private redirectService: RedirectService,
    private clipboard: Clipboard,
    private snackBar: MatSnackBar
  ) {
    data = data ? data : [];
    this.originalData = data ?? {};
  }

  ngOnInit(): void {
    this.viewData = this.buildViewData(this.originalData);
  }

  public copyRawJson() {
    this.clipboard.copy(JSON.stringify(this.data));
    this.showSnackbar();
  }

  public copyHash(hash: string): void {
    this.clipboard.copy(hash);
    this.showSnackbar();
  }

  public redirect(event: MouseEvent, path: string, id: string): void {
    this.redirectService.redirect(path, id, event);
  }

  private showSnackbar(): void {
    this.snackBar.open('Copiado para a área de transferência', 'OK', {
      duration: 3000,
      horizontalPosition: 'start',
      verticalPosition: 'bottom',
      panelClass: ['grey-snackbar'],
    });
  }

  public groupRestrictionsByType(restrictions: any[]) {
    const grouped = _.groupBy(restrictions, restriction => restriction.tipoRestricao);
    const groupedRestrictions = Object.entries(grouped).map((element) => {
      return {
        tipoRestricao: element[0],
        valores: element[1],
      }
    });
    return groupedRestrictions
  }

  /** Remove o objeto configuracaoPrecificacao quando o sistEmis atual não for FRETAMENTO e exigir FRETAMENTO em SIST_EMIS*/
  private shouldDropConfigItem(cfg: any): boolean {
    const restricoesComparadas = cfg?.motivoExclusaoVoo?.restricoesComparadas;

    if (!Array.isArray(restricoesComparadas)) {
      return false;
    }

    return restricoesComparadas.some((c: any) => {
      if (c?.tipoRestricao !== 'SIST_EMIS') {
        return false;
      }

      const conf = c?.restricoesConfig;

      if (Array.isArray(conf)) {
        return conf.includes('FRETAMENTO');
      }

      return conf === 'FRETAMENTO';
    });
  }

  /** Clona e filtra o objeto 'configuracoesEMotivos' quando o sistEmis for diferente de FRETAMENTO */
  private buildViewData(src: any) {
    const cloned = _.cloneDeep(src) || {};
    const sistEmis = cloned?.precificavelRE?.sistEmis;

    if (sistEmis && sistEmis !== 'FRETAMENTO') {
      const path = cloned?.precificacaoTesteResponse?.resposta?.resultadoDebug;
      const list = path?.configuracoesEMotivos;

      if (Array.isArray(list)) {
        path.configuracoesEMotivos = list.filter((item: any) => !this.shouldDropConfigItem(item));
      }
    }

    return cloned;
  }

  public trackByConfig = (index: number, item: any) =>
    item?.motivoExclusaoVoo?.idConfiguracaoPrecificacao ??
    item?.configuracaoPrecificacao?.id ?? index;

  public trackByIndex = (i: number) => i;
}
