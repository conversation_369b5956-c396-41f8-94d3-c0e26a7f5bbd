<app-simple-list #simpleList apiRoute="configuracaoRc" name="configuracaoRc"
  [filterDialogComponent]="filterTemplate" [profileReceived]="profileReceived" [queryParams]="queryParams">
  <ng-template #listTemplate let-item="item">
    <div fxLayout="column" style="padding: 20px">
      <small>Prioridade: {{item.prioridade}}</small>
      <span style="color: black;">{{ item.nome }}</span>
      <small *ngIf="item.restricoes.length" style="font-size:8pt;">{{item.restricoes | restriction}}</small>
    </div>
  </ng-template>

  <ng-template #filterTemplate let-data>
    <app-simple-filter-dialog [data]="data" [dialogClose]="data.dialogClose" [formDefault]="{ nome: '', ciaAerea: '', empresa: '', agencias : '', rentabilidadeMax: '', tipoConfigPrecificacao:'', status:'', ativo:'',  produto:''}">
      <ng-template #filtersTemplate let-getFormControl='getFormControl'>
        <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="space-between center">
          <app-input-field fxFlex="100" maxlength="100"
                           label="Nome" [control]="getFormControl('nome')">
          </app-input-field>
          <app-search-box fxFlex="100" [formControl]="getFormControl('ciaAerea')" resource="ciasAereas/combo" displayField="nomeTratado"
            valueField="codigo" placeholder="Cia Aérea" [searchFields]="['codigo', 'nome']">
          </app-search-box>
        </div>
        <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="space-between center">
            <app-search-box resource="empresas" [searchFields]="['referencia', 'nome']" displayField="nomeTratado" [multiple]="true"
            [formControl]="getFormControl('empresa')" valueField="nome" placeholder="Empresa" fxFlex="100" >
          </app-search-box>
          <app-input-field fxFlex="100" maxlength="100"
                           label="Comissão" [control]="getFormControl('rentabilidadeMax')">
          </app-input-field>
          </div>

        <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="space-between center">
            <app-search-box [formControl]="getFormControl('agencias')" resource="agencias" displayField="nomeTratado"
              valueField="nomeFantasia" [searchFields]="['nomeFantasia', 'referencia']" placeholder="Clientes" fxFlex="100">
            </app-search-box>
        </div>
        <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="space-between center">
          <app-search-box fxFlex="50" [formControl]="getFormControl('status')" displayField="nome"
          placeholder="Status de configuração" valueField="id"
          [options]="[{id: 'NAO_PUBLICADA', nome: 'Não publicados'},
          {id: 'PUBLICADA', nome: 'Publicados'},
          {id: 'EM_HISTORICO', nome: 'Em Histórico'}]">
          </app-search-box>
          <app-search-box fxFlex="50" [formControl]="getFormControl('produto')" resource="produtos" displayField="nome"
          [searchFields]="['nome']" valueField="nome" placeholder="Produto" [pageSize]="100">
        </app-search-box>
        </div>

      </ng-template>
    </app-simple-filter-dialog>
  </ng-template>

</app-simple-list>



