import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ChangePasswordComponent } from './core/components/change-password/change-password.component';
import { LoginComponent } from './core/components/login/login.component';
import { NotFoundComponent } from './core/components/not-found/not-found.component';
import { PasswordRecoverComponent } from './core/components/password-recover/password-recover.component';
import { RedirectComponent } from './core/components/redirect/redirect.component';
import { VerifyCodeComponent } from './core/components/verify-code/verify-code.component';
import { AuthGuard } from './core/guards/auth.guard';
import { CodeVerifyGuard } from './core/guards/code-verify.guard';
import { PermissionGuard } from './core/guards/permission.guard';

const routes: Routes = [
  {
    path: '',
    loadChildren: () =>
      import('./features/home/<USER>').then((mod) => mod.HomeModule),
    pathMatch: 'full',
    canActivate: [AuthGuard],
    data: { breadcrumb: 'Home' },
  },
  {
    path: 'markupEdit',
    loadChildren: () =>
      import('./features/markup-edit/markup-edit.module').then(
        (mod) => mod.MarkupEditModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Edição de Markup' },
  },
  {
    path: 'repositories',
    loadChildren: () =>
      import('./features/file-repository/file-repository.module').then(
        (mod) => mod.FileRepositoryModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Repositório de contratos' },
  },
  {
    path: 'distributor',
    loadChildren: () =>
      import('./features/distributor/distributor.module').then(
        (mod) => mod.DistributorModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Distribuidor de conteúdo' },
  },
  {
    path: 'company',
    loadChildren: () =>
      import('./features/company/company.module').then(
        (mod) => mod.CompanyModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Empresas' },
  },
  {
    path: 'operationalUnit',
    loadChildren: () =>
      import('./features/operational-unit/operational-unit.module').then(
        (mod) => mod.OperationalUnitModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Unidades Operacionais' },
  },
  {
    path: 'agency',
    loadChildren: () =>
      import('./features/agency/agency.module').then((mod) => mod.AgencyModule),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Agências' },
  },
  {
    path: 'subsidiary',
    loadChildren: () =>
      import('./features/subsidiary/subsidiary.module').then(
        (mod) => mod.SubsidiaryModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Filiais' },
  },
  {
    path: 'contact',
    loadChildren: () =>
      import('./features/contact/contact.module').then(
        (mod) => mod.ContactModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Contatos' },
  },
  {
    path: 'contract',
    loadChildren: () =>
      import('./features/contract/contract.module').then(
        (mod) => mod.ContractModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Contratos' },
  },
  {
    path: 'ptc',
    loadChildren: () =>
      import('./features/ptc/ptc.module').then((mod) => mod.PtcModule),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'PTC' },
  },
  {
    path: 'product',
    loadChildren: () =>
      import('./features/product/product.module').then(
        (mod) => mod.ProductModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Produtos' },
  },
  {
    path: 'user-front',
    loadChildren: () =>
      import('./features/user-front/user-front.module').then(
        (mod) => mod.UserFrontModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Usuários Web' },
  },
  {
    path: 'group',
    loadChildren: () =>
      import('./features/group/group.module').then((mod) => mod.GroupModule),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Grupos' },
  },
  {
    path: 'integrators',
    loadChildren: () =>
      import('./features/integrator/integrator.module').then(
        (mod) => mod.IntegratorModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Integrators' },
  },
  {
    path: 'credentialCapture',
    loadChildren: () =>
      import('./features/credential-capture/credential-capture.module').then(
        (mod) => mod.CredentialCaptureModule
      ),
    data: { breadcrumb: 'Credencial de Captura' },
  },
  {
    path: 'credenciais',
    loadChildren: () =>
      import('./features/credentials/credentials.module').then(
        (mod) => mod.CredentialsModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Credenciais' },
  },
  {
    path: 'passenger',
    loadChildren: () =>
      import('./features/pass/passenger-front.module').then(
        (mod) => mod.PassengerFrontModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Passageiros White-List' },
  },
  {
    path: 'credencialPaymentHub',
    loadChildren: () =>
      import(
        './features/credential-payment-hub/credential-payment-hub.module'
      ).then((mod) => mod.CredentialPaymentHubModule),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Credencial Payment Hub' },
  },
  {
    path: 'usuariosIntegradores',
    loadChildren: () =>
      import('./features/integration-users/integration-users.module').then(
        (mod) => mod.IntegrationUsersModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Usuários Integradores' },
  },
  {
    path: 'aircompanies',
    loadChildren: () =>
      import('./features/aircompanies/aircompanies.module').then(
        (mod) => mod.AirCompaniesModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Cias. Aéreas' },
  },
  {
    path: 'audit',
    loadChildren: () =>
      import('./features/audit/audit.module').then((mod) => mod.AuditModule),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Auditorias' },
  },
  {
    path: 'emissionSystemBlock',
    loadChildren: () =>
      import(
        './features/emission-system-block/emission-system-block.module'
      ).then((mod) => mod.EmissionSystemBlockModule),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Bloqueio de Sistema Emissor' },
  },
  {
    path: 'reservationImport',
    loadChildren: () =>
      import('./features/reservation-import/reservation-import.module').then(
        (mod) => mod.ReservationImportModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Importação de Loc - Acordo Padrão' },
  },
  {
    path: 'senhaServidorEmail',
    loadChildren: () =>
      import('./features/server-users/senha-servidor-email.module').then(
        (mod) => mod.SenhaServidorEmailModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Senha servidor email' },
  },
  {
    path: 'duSettings',
    loadChildren: () =>
      import('./features/du-settings/du-settings.module').then(
        (mod) => mod.DuSettingsModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'DU\'s' },
  },
  {
    path: 'grupos-de-cias-aereas',
    loadChildren: () =>
      import('./features/aircompany-groups/aircompany-groups.module').then(
        (mod) => mod.AircompanyGroupsModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Grupos de Cias. Aéreas' },
  },
  {
    path: 'boardingFee',
    loadChildren: () =>
      import('./features/boarding-fee/boarding-fee.module').then(
        (mod) => mod.BoardingFeeModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Taxas de Embarque' },
  },
  {
    path: 'tradeAgreements',
    loadChildren: () =>
      import('./features/trade-agreements/trade-agreements.module').then(
        (mod) => mod.TradeAgreementsModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Acordos Comerciais' },
  },
  {
    path: 'routes',
    loadChildren: () =>
      import('./features/routes/routes.module').then((mod) => mod.RoutesModule),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Configurações de Rotas' },
  },
  {
    path: 'routesV2',
    loadChildren: () =>
      import('./features/routesV2/routesV2.module').then(
        (mod) => mod.RoutesV2Module
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Configurações de Rotas V2' },
  },
  {
    path: 'programFareProfile',
    loadChildren: () =>
      import(
        './features/program-fare-profiles/program-fare-profiles.module'
      ).then((mod) => mod.ProgramFareProfilesModule),
    data: { breadcrumb: 'Perfis Tarifarios - Por Programa' },
  },
  {
    path: 'comparePriceSolicitations',
    loadChildren: () =>
      import(
        './features/compare-price-solicitations/compare-price-solicitations.module'
      ).then((mod) => mod.ComparePriceSolicitationsModule),
    data: { breadcrumb: 'Solicitações de Comparativos de Preços' },
  },
  // { path: 'comparePrice',
  //   loadChildren: () => import('./features/compare-price/compare-price.module').then(mod => mod.ComparePriceModule),
  //   canActivate: [PermissionGuard],
  //   data: { breadcrumb: 'Comparativos de Preços' }},
  {
    path: 'credencialScheduleChange',
    loadChildren: () =>
      import(
        './features/credential-schedule-change/credential-schedule-change.module'
      ).then((mod) => mod.CredentialScheduleChangeModule),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Credencial de Schedule Change' },
  },
  {
    path: 'configuracaofee',
    loadChildren: () =>
      import('./features/fee-settings/fee-settings.module').then(
        (mod) => mod.FeeSettingsModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Fee\'s' },
  },
  {
    path: 'condicoes-de-pagamento',
    loadChildren: () =>
      import('./features/payment-terms/payment-terms.module').then(
        (mod) => mod.PaymentTermsModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Condições de Pagamento' },
  },
  {
    path: 'searchSettings',
    loadChildren: () =>
      import('./features/search-settings/search-settings.module').then(
        (mod) => mod.SearchSettingsModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Configurações de Busca' },
  },
  {
    path: 'ravs',
    loadChildren: () =>
      import('./features/ravs/ravs.module').then((mod) => mod.RavsModule),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Rav\'s' },
  },
  {
    path: 'configuracoesPrecificacaoFretamento',
    loadChildren: () =>
      import(
        './features/pricing-charter-configuration/pricing-charter-configuration.module'
      ).then((mod) => mod.PricingCharterConfigurationModule),
    data: { breadcrumb: 'Precificação do Fretamento' },
  },
  {
    path: 'pricingSettings',
    loadChildren: () =>
      import('./features/pricing-settings/pricing-settings.module').then(
        (mod) => mod.PricingSettingsModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Precificações' },
  },
  {
    path: 'ravs',
    loadChildren: () =>
      import('./features/ravs/ravs.module').then((mod) => mod.RavsModule),
    data: { breadcrumb: 'Rav\'s' },
  },
  {
    path: 'availabilitySearch',
    loadChildren: () =>
      import('./features/availability-search/availability-search.module').then(
        (mod) => mod.AvailabilitySearchModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Busca de Disponibilidade' },
  },
  {
    path: 'rateProfiles',
    loadChildren: () =>
      import('./features/rate-profiles/rate-profiles.module').then(
        (mod) => mod.RateProfilesModule
      ),
    data: { breadcrumb: 'Perfil Tarifario' },
  },
  {
    path: 'depuracaoReserva',
    loadChildren: () =>
      import('./features/debug-reserve/debug-reserve.module').then(
        (mod) => mod.DebugReservesModule
      ),
    data: { breadcrumb: 'Depuração de Reserva' },
  },
  {
    path: 'aeroportos',
    loadChildren: () =>
      import('./features/airports/airports.module').then(
        (mod) => mod.AirPortsModule
      ),
    data: { breadcrumb: 'Aeroportos' },
  },
  {
    path: 'commercialPricingSettings',
    loadChildren: () =>
      import(
        './features/commercial-pricing-settings/commercial-pricing-settings.module'
      ).then((mod) => mod.CommercialPricingSettingsModule),
    data: { breadcrumb: 'Precificações Comerciais' },
  },
  {
    path: 'pricingIntegration',
    loadChildren: () =>
      import('./features/pricing-integration/pricing-integration.module').then(
        (mod) => mod.PricingIntegrationModule
      ),
    data: { breadcrumb: 'Precificacão Integração' },
  },

  {
    path: 'registrationContinente',
    loadChildren: () =>
      import(
        './features/registration-continente/registration-continente.module'
      ).then((mod) => mod.RegistrationContinenteModule),
    data: { breadcrumb: 'Cadastro continente' },
  },
  {
    path: 'registrationCountry',
    loadChildren: () =>
      import(
        './features/registration-country/registration-country.module'
      ).then((mod) => mod.RegistrationCountryModule),
    data: { breadcrumb: 'Cadastro país' },
  },
  {
    path: 'registrationState',
    loadChildren: () =>
      import('./features/registration-state/registration-state.module').then(
        (mod) => mod.RegistrationStateModule
      ),
    data: { breadcrumb: 'Cadastro Estado' },
  },
  {
    path: 'perfis',
    loadChildren: () =>
      import('./features/profiles/profiles.module').then(
        (mod) => mod.ProfilesModule
      ),
    data: { breadcrumb: 'Perfis' },
  },
  {
    path: 'companyPermission',
    loadChildren: () =>
      import('./features/company-permissions/company-permissions.module').then(
        (mod) => mod.CompanyPermissionsModule
      ),
    data: { breadcrumb: 'Empresas Permissoes' },
  },
  {
    path: 'locCancel',
    loadChildren: () =>
      import('./features/loc-cancel/loc-cancel.module').then(
        (mod) => mod.LocCancelModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Cancelamento de localizadores' },
  },
  {
    path: 'sistema-emissor-inativo-busca-disp',
    loadChildren: () =>
      import(
        './features/inactive-emitter-system-availability-search/inactive-emitter-system-availability-search.module'
      ).then((mod) => mod.InactiveEmitterSystemAvailabilitySearchModule),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Sistema Emissor Inativo Busca Disponibilidade' },
  },
  {
    path: 'flag-feature',
    loadChildren: () =>
      import('./features/flag-feature/flag-feature.module').then(
        (mod) => mod.FlagFeatureModule
      ),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Flag Feature' },
  },
  {
    path: 'airlineTariffChecker',
    loadChildren: () =>
      import(
        './features/airline-tariff-checker/airline-tariff-checker.module'
      ).then((mod) => mod.AirlineTariffCheckerModule),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Airline Tariff Checker' },
  },
  {
    path: 'emissionSystemVhiPlus',
    loadChildren: () =>
      import(
        './features/emission-system-vhi-plus/emission-system-vhi-plus.module'
      ).then((mod) => mod.EmissionSystemVhiPlusModule),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Sistema Emissor VHI Plus' },
  },
  { path: 'filtroRecomendacoes',
    loadChildren: () => import('./features/filtroRecomendacoes/filtroRecomendacoes.module').then(mod => mod.FiltroRecomendacoesModule),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Filtro de Recomendacoes' }},
  {
    path: 'configuracaoProduto',
    loadChildren: () =>
      import(
        './features/product-settings/product-settings.module'
      ).then((mod) => mod.ProductSettingsModule),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Bloqueio venda de Aéreo sem Hotel' },
  },
  {
    path: 'localidades',
    loadChildren: () =>
      import(
        './features/localidade/localidade.module'
      ).then((mod) => mod.LocalidadeModule),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Localidades' },

  },
  {
    path: 'configuracaoRc',
    loadChildren: () =>
      import(
        './features/rc-settings/rc-settings.module'
      ).then((mod) => mod.RcSettingsModule),
    canActivate: [PermissionGuard],
    data: { breadcrumb: 'Configuração RC' },
  },
  { path: 'login', component: LoginComponent, data: { breadcrumb: 'Login' } },
  {
    path: 'changePassword',
    component: ChangePasswordComponent,
    data: { breadcrumb: 'Alterar Senha' },
  },
  {
    path: 'alterar-senha',
    component: PasswordRecoverComponent,
    data: { breadcrumb: 'Criar senha' },
  },
  {
    path: 'redirect',
    component: RedirectComponent,
    data: { breadcrumb: 'Redirect' },
  },
  {
    path: 'verifyCode',
    canActivate: [CodeVerifyGuard],
    component: VerifyCodeComponent,
    data: { breadcrumb: 'Autenticação' },
  },
  {
    path: '404',
    component: NotFoundComponent,
    data: { breadcrumb: 'Página não encontrada' },
  },
  { path: '**', redirectTo: '/404' },
  {
    path: 'sample',
    loadChildren: () =>
      import('./features/sample/sample.module').then((mod) => mod.SampleModule),
  },
];

// if (isDevMode()) {
//   routes.unshift({ path: 'sample',
//     loadChildren: () => import('./features/sample/sample.module').then(mod => mod.SampleModule)});
// }

@NgModule({
  imports: [RouterModule.forRoot(routes, { relativeLinkResolution: 'legacy' })],
  exports: [RouterModule],
})
export class AppRoutingModule {}
